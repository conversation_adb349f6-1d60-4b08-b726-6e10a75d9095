// React Imports
import type { SVGAttributes } from 'react'

const Drib<PERSON> = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='91' height='23' viewBox='0 0 91 23' fill='none' {...props}>
      <path
        d='M27.8171 9.29942C28.8495 9.29942 29.6863 8.46351 29.6863 7.43253C29.6863 6.40139 28.8495 5.56567 27.8171 5.56567C26.7847 5.56567 25.9478 6.40139 25.9478 7.43253C25.9478 8.46355 26.7847 9.29942 27.8171 9.29942ZM89.5081 15.6778C89.2238 15.4782 89.0042 15.4451 88.8221 15.8328C85.6825 22.6219 80.3919 19.234 80.9174 19.5322C82.0909 18.9962 85.1774 16.5248 84.7109 13.1123C84.4276 11.0268 82.6348 10.1017 80.7319 10.4329C77.4102 11.0111 76.1882 14.5883 76.8103 17.7553C76.9192 18.2991 77.1137 18.7477 77.308 19.1871C73.5543 22.2348 72.0637 16.4588 71.8945 15.7686C71.8878 15.7317 74.7929 13.314 75.6009 7.55306C76.4477 1.51519 74.5186 0.415758 72.5459 0.450085C68.8954 0.513595 67.909 8.12861 69.2343 14.4752C69.1232 14.504 68.6067 14.7897 67.7767 14.8217C67.1795 12.9454 64.6275 11.3006 63.9596 11.9344C62.288 13.5198 64.365 16.6194 65.8264 16.8625C64.9485 22.2612 59.4582 20.9242 60.4802 14.1605C62.2681 10.8456 63.6279 5.91661 63.3852 2.94102C63.2993 1.88755 62.5192 0.476125 60.7549 0.546125C57.3615 0.680166 56.9926 8.2973 57.3904 13.7035C57.3707 13.5703 57.1818 14.3603 55.7912 14.7531C55.4623 12.9302 52.5226 11.1004 51.8305 11.9802C50.5351 13.6266 52.7801 16.5623 53.8436 16.767C52.9656 22.1655 47.4755 20.8284 48.4976 14.0648C50.2854 10.7501 51.645 5.8211 51.4024 2.84551C51.3164 1.79188 50.5365 0.380411 48.7721 0.450248C45.3786 0.584452 45.0097 8.20159 45.4076 13.6078C45.3876 13.4725 45.1944 14.2886 43.7471 14.6748C43.6986 12.3098 40.7481 11.2254 40.0393 11.98C38.7762 13.3251 40.3286 16.0851 41.7649 16.767C40.887 22.1655 35.3969 20.8284 36.4188 14.0648C38.2067 10.7501 39.5665 5.8211 39.3237 2.84551C39.2379 1.79188 38.4578 0.380411 36.6933 0.450248C33.3001 0.584452 33.0271 8.58453 33.4249 13.9906C32.3072 18.7718 28.559 24.7426 29.0459 12.7817C29.094 11.9427 29.1465 11.6242 28.7276 11.31C28.4136 11.0658 27.6998 11.1833 27.3095 11.193C26.8352 11.212 26.7163 11.4891 26.6115 11.9079C26.3673 12.9893 26.3234 14.0377 26.2885 15.4682C26.2657 16.1373 26.2119 16.4495 25.9538 17.3619C25.696 18.2742 24.2255 19.9415 23.4203 19.6627C22.3034 19.2793 22.6698 16.131 22.8791 13.9679C23.0535 12.2585 22.495 11.4908 21.0641 11.2117C20.2264 11.0373 19.7174 11.0641 18.8449 10.7895C18.0199 10.5299 17.8333 8.97171 16.0736 9.49098C15.1112 9.77522 15.7299 11.8113 15.4986 13.3204C14.3614 20.7437 11.9951 20.9475 10.8973 17.3415C15.8414 5.25151 12.3275 0.485268 10.2707 0.485268C8.1283 0.485268 5.6794 1.95861 6.71638 11.3856C6.21218 11.2387 6.05712 11.1596 5.50516 11.1596C2.38345 11.1596 0.256592 13.6792 0.256592 16.7874C0.256592 19.8956 2.38361 22.4155 5.50536 22.4155C7.34822 22.4155 8.64206 21.5787 9.62197 20.2842C10.2613 21.1984 11.0398 22.4295 12.4636 22.3741C16.7072 22.2091 17.9413 13.5179 18.087 13.0331C18.5407 13.1029 18.9699 13.2351 19.3888 13.3049C20.0869 13.4095 20.1376 13.6854 20.1217 14.3864C19.9367 20.2974 21.0292 22.3671 23.5074 22.3671C24.8881 22.3671 26.1187 21.0126 26.9665 20.0441C27.5997 21.3489 28.6087 22.3271 29.9622 22.3669C33.2422 22.4484 34.498 17.2288 34.3835 17.9157C34.2936 18.4546 35.4476 22.3375 38.8243 22.3516C43.0069 22.3692 43.7844 17.7767 43.8771 17.0075C43.8887 16.8544 43.8938 16.8703 43.8771 17.0075L43.8739 17.0541C45.2018 16.8075 45.887 16.0966 45.887 16.0966C45.887 16.0966 46.9532 22.423 50.9028 22.3517C55.0042 22.2775 55.7777 18.1262 55.8791 17.3167C55.8925 17.1246 55.9004 17.1471 55.8791 17.3167L55.8775 17.3405C57.4549 16.7675 57.8698 16.1925 57.8698 16.1925C57.8698 16.1925 58.7172 22.3931 62.8856 22.4473C66.6002 22.4959 67.9769 18.6979 67.985 17.1083C68.6115 17.115 69.7704 16.7371 69.7432 16.7156C69.7432 16.7156 71.1039 22.137 74.9035 22.4155C76.6875 22.5462 78.0258 21.4131 78.7887 20.8964C80.5814 22.3459 86.5513 24.1975 90.3208 17.8166C90.8529 16.9009 89.709 15.8191 89.5081 15.6778ZM5.35471 20.3106C3.53336 20.3106 2.36549 18.6291 2.36549 16.8161C2.36549 15.0033 3.43753 13.3218 5.25887 13.3218C6.07847 13.3218 6.53438 13.4118 7.17271 13.9661C7.28847 14.4215 7.61651 15.472 7.77614 15.9491C7.99002 16.5874 8.24442 17.1308 8.50091 17.7222C8.13446 19.239 6.93312 20.3106 5.35471 20.3106ZM9.79303 14.0194C9.71728 13.8989 9.73312 13.973 9.64838 13.8592C9.3145 12.9524 8.67112 10.9284 8.59659 8.62955C8.51238 6.02902 8.94646 2.98078 10.2263 2.98078C11.0934 2.98078 12.015 9.15853 9.79283 14.0194H9.79303ZM35.4379 11.3098C35.2325 9.76661 35.2217 2.88702 36.8759 3.07649C37.7892 3.44588 36.2968 9.93759 35.4379 11.3098ZM47.5166 11.3098C47.3112 9.76661 47.3004 2.88702 48.9546 3.07649C49.8679 3.44588 48.3755 9.93759 47.5166 11.3098ZM59.4995 11.4057C59.2939 9.86228 59.2833 2.98274 60.9372 3.1722C61.8506 3.54159 60.3581 10.0334 59.4995 11.4057ZM72.7284 2.67384C74.2407 2.51731 74.1784 9.11118 71.1428 13.2741C70.7514 11.7694 70.1511 3.19045 72.7284 2.67367V2.67384ZM79.085 16.8625C78.5993 14.4127 79.8544 12.8037 81.1482 12.6273C81.6005 12.5552 82.2558 12.8477 82.3866 13.3946C82.6015 14.4252 82.3554 15.954 79.4595 17.8937C79.4638 17.9102 79.1933 17.4082 79.0852 16.8625H79.085Z'
        fill='currentColor'
      />
    </svg>
  )
}

export default Dribbble
