// React Imports
import type { SVGAttributes } from 'react'

const HubSpot = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg width='107' height='39' viewBox='0 0 339 96' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M36.677 54.332H11.81V80.53H0V15.89h11.81v27.08h24.867V15.89h11.807v64.638H36.677zm49.593 5.304c0 5.384-4.386 9.764-9.768 9.764-5.384 0-9.766-4.38-9.766-9.764V31.951H55.553v27.685c0 11.55 9.398 20.946 20.949 20.946 11.548 0 20.946-9.395 20.946-20.946V31.951H86.269zm82.976-24.834c0-5.676 3.757-7.476 7.87-7.476 3.312 0 7.695 2.52 10.554 5.583l7.333-8.644c-3.664-4.951-11.088-8.374-17.17-8.374-12.168 0-20.934 7.114-20.934 18.91 0 21.881 26.748 14.946 26.748 27.195 0 3.777-3.666 7.112-7.869 7.112-6.622 0-8.77-3.241-11.81-6.664l-8.142 8.463c5.191 6.394 11.63 9.637 19.324 9.637 11.539 0 20.843-7.204 20.843-18.461 0-24.311-26.747-16.747-26.747-27.281M334.72 70.203c-6.616 0-8.495-2.861-8.495-7.246v-19.41h10.285v-9.84h-10.285V20.736l-11.357 5.098v39.54c0 10.109 6.974 15.209 16.542 15.209 1.432 0 3.402-.093 4.476-.358l2.774-10.197c-1.252.087-2.683.176-3.94.176M128.893 32.275c-5.546 0-9.418 1.61-13.157 5.28V16.277h-11.218v39.435c0 14.76 10.671 24.87 22.662 24.87 13.329 0 25.008-10.289 25.008-24.152 0-13.688-10.769-24.155-23.295-24.155m-.069 37.017c-7.028 0-12.724-5.697-12.724-12.724s5.696-12.723 12.724-12.723c7.026 0 12.723 5.696 12.723 12.723s-5.697 12.724-12.723 12.724m121.859-13.445c0-13.863-11.678-24.152-25.007-24.152-11.991 0-22.663 10.11-22.663 24.87V96h11.218V74.722c3.739 3.67 7.611 5.28 13.158 5.28 12.525 0 23.294-10.467 23.294-24.155m-10.641-.138c0 7.026-5.696 12.723-12.723 12.723s-12.724-5.696-12.724-12.723 5.696-12.724 12.724-12.724c7.027 0 12.723 5.697 12.723 12.724 M286.932 31.152V19.883c2.942-1.39 5.002-4.365 5.002-7.818v-.26c0-4.766-3.899-8.666-8.664-8.666h-.261c-4.765 0-8.665 3.9-8.665 8.665v.26c0 3.454 2.062 6.43 5.003 7.82v11.268a24.6 24.6 0 0 0-11.683 5.14l-30.938-24.067c.203-.784.346-1.591.347-2.439.007-5.398-4.363-9.779-9.761-9.786s-9.78 4.364-9.787 9.761c-.006 5.398 4.364 9.78 9.762 9.787 1.759.002 3.387-.498 4.814-1.31l30.435 23.676a24.6 24.6 0 0 0-4.104 13.625 24.57 24.57 0 0 0 4.482 14.165l-9.256 9.256c-.731-.22-1.491-.373-2.295-.373a8.032 8.032 0 1 0 8.032 8.032c0-.803-.153-1.563-.373-2.295l9.155-9.155c4.156 3.172 9.331 5.078 14.963 5.078 13.646 0 24.708-11.062 24.708-24.708 0-12.353-9.075-22.559-20.916-24.388m-3.792 37.054c-6.996 0-12.668-5.671-12.668-12.667s5.672-12.668 12.668-12.668 12.668 5.672 12.668 12.668-5.672 12.667-12.668 12.667'
        fill='currentColor'
      />
    </svg>
  )
}

export default HubSpot
