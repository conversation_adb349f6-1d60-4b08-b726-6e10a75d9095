// React Imports
import type { SVGAttributes } from 'react'

const Airbnb = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg width='91' height='29' viewBox='0 0 91 29' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        id='Logo'
        d='M47.8232 7.48315C47.8232 8.49225 47.0101 9.30477 46.001 9.30477C44.9919 9.30477 44.1793 8.49225 44.1793 7.48315C44.1793 6.47404 44.9642 5.66152 46.001 5.66152C47.0386 5.68952 47.8232 6.50236 47.8232 7.48315ZM40.3115 11.155V11.6036C40.3115 11.6036 39.4429 10.4819 37.593 10.4819C34.5383 10.4819 32.1556 12.8084 32.1556 16.0314C32.1556 19.2267 34.5103 21.5809 37.593 21.5809C39.4709 21.5809 40.3115 20.4318 40.3115 20.4318V20.908C40.3115 21.1323 40.48 21.3 40.704 21.3H42.974V10.7613H40.704C40.48 10.7622 40.3115 10.9587 40.3115 11.155ZM40.3115 17.9093C39.8915 18.5261 39.0504 19.0587 38.0416 19.0587C36.248 19.0587 34.8743 17.9376 34.8743 16.0314C34.8743 14.1254 36.2479 13.0044 38.0416 13.0044C39.0227 13.0044 39.9195 13.5649 40.3115 14.1535V17.9093ZM44.6559 10.7622H47.3463V21.3009H44.6559V10.7622ZM84.8477 10.4816C82.9978 10.4816 82.1287 11.6033 82.1287 11.6033V5.68952H79.4382V21.3009H81.7087C81.933 21.3009 82.1007 21.1046 82.1007 20.9083V20.4321C82.1007 20.4321 82.9698 21.5812 84.8191 21.5812C87.8744 21.5812 90.2566 19.2275 90.2566 16.0322C90.2566 12.8369 87.8745 10.4816 84.8477 10.4816ZM84.3991 19.0304C83.3618 19.0304 82.5492 18.4984 82.1287 17.8813V14.1255C82.5492 13.5649 83.446 12.9764 84.3991 12.9764C86.1927 12.9764 87.5661 14.0975 87.5661 16.0034C87.5661 17.9093 86.1931 19.0304 84.3991 19.0304ZM78.0371 15.0509V21.3289H75.3461V15.3588C75.3461 13.6215 74.7855 12.9204 73.273 12.9204C72.4604 12.9204 71.6193 13.3409 71.086 13.9577V21.3011H68.3961V10.7625H70.5254C70.7497 10.7625 70.918 10.9588 70.918 11.155V11.6036C71.7028 10.7905 72.7396 10.4819 73.7764 10.4819C74.9538 10.4819 75.9349 10.8188 76.7197 11.4913C77.6723 12.2761 78.0371 13.2849 78.0371 15.0509L78.0371 15.0509ZM61.8646 10.4816C60.0153 10.4816 59.1462 11.6033 59.1462 11.6033V5.68952H56.4557V21.3009H58.7256C58.9499 21.3009 59.1182 21.1046 59.1182 20.9083V20.4321C59.1182 20.4321 59.9873 21.5812 61.8366 21.5812C64.8919 21.5812 67.2741 19.2275 67.2741 16.0322C67.3021 12.8367 64.9199 10.4816 61.8646 10.4816ZM61.4161 19.0304C60.3793 19.0304 59.5667 18.4984 59.1462 17.8813V14.1255C59.5667 13.5649 60.4635 12.9764 61.4161 12.9764C63.2103 12.9764 64.5834 14.0975 64.5834 16.0034C64.5834 17.9093 63.2103 19.0304 61.4161 19.0304ZM54.1292 10.4816C54.9417 10.4816 55.3623 10.6222 55.3623 10.6222V13.1163C55.3623 13.1163 53.1201 12.3595 51.7191 13.9574V21.3286H49.028V10.7621H51.2984C51.5227 10.7621 51.6904 10.9584 51.6904 11.1547V11.6033C52.1955 11.0144 53.2887 10.4816 54.1292 10.4816L54.1292 10.4816ZM26.1861 20.3478C26.0461 20.0115 25.9058 19.6473 25.7658 19.3387C25.5412 18.8344 25.317 18.3576 25.1213 17.9093L25.0932 17.8813C23.159 13.6769 21.0854 9.41706 18.8989 5.21322L18.815 5.0447C18.585 4.60859 18.3609 4.16941 18.1427 3.72729C17.8621 3.22246 17.5821 2.69051 17.1335 2.18565C16.2367 1.06454 14.9474 0.447754 13.5743 0.447754C12.1726 0.447754 10.9115 1.06458 9.98701 2.12938C9.56641 2.63367 9.25789 3.16619 8.97787 3.67102C8.75913 4.11286 8.53504 4.55203 8.30563 4.98843L8.22133 5.15695C6.06316 9.36079 3.96122 13.6209 2.02731 17.8251L1.99903 17.8808C1.80301 18.3298 1.57847 18.8061 1.35421 19.3101C1.21422 19.6187 1.07418 19.955 0.93419 20.3192C0.569649 21.3561 0.457359 22.3371 0.597929 23.3465C0.906198 25.4487 2.3073 27.2138 4.2415 27.9992C4.97033 28.3077 5.72715 28.4478 6.51171 28.4478C6.73597 28.4478 7.01596 28.4198 7.2405 28.3914C8.16564 28.2795 9.11847 27.9715 10.0435 27.4389C11.1926 26.7941 12.2857 25.8696 13.5188 24.5242C14.7519 25.8696 15.873 26.7941 16.9941 27.4389C17.9195 27.9715 18.8721 28.2794 19.7969 28.3914C20.0212 28.42 20.3017 28.4477 20.526 28.4477C21.3108 28.4477 22.0953 28.3078 22.7959 27.9992C24.7581 27.2138 26.1312 25.4207 26.4397 23.3465C26.6623 22.3657 26.5503 21.3851 26.1861 20.3478ZM13.546 21.8051C12.0324 19.8992 11.0512 18.1056 10.7152 16.5925C10.5753 15.9477 10.547 15.3871 10.6313 14.8826C10.687 14.434 10.8555 14.0415 11.0798 13.7052C11.6124 12.9489 12.5092 12.4721 13.5463 12.4721C14.5837 12.4721 15.5085 12.9206 16.013 13.7052C16.2373 14.0415 16.4053 14.4343 16.4618 14.8826C16.5456 15.3874 16.5176 15.9757 16.3776 16.5925C16.0402 18.0776 15.0591 19.8715 13.546 21.8051ZM24.7284 23.1226C24.5324 24.5799 23.5513 25.8415 22.1783 26.4021C21.5057 26.6821 20.7766 26.7663 20.0483 26.6821C19.3478 26.5978 18.6466 26.3735 17.9184 25.9536C16.9093 25.3924 15.9005 24.5242 14.7231 23.2345C16.5727 20.9646 17.6938 18.8901 18.1144 17.0408C18.3107 16.1717 18.339 15.3871 18.2544 14.658C18.1426 13.9575 17.8901 13.3126 17.4978 12.7521C16.6284 11.4904 15.1708 10.7621 13.5457 10.7621C11.9206 10.7621 10.463 11.519 9.59415 12.7521C9.20191 13.3126 8.94934 13.9574 8.8373 14.658C8.72533 15.3871 8.75303 16.1997 8.97733 17.0408C9.39756 18.8901 10.5467 20.9923 12.3683 23.2625C11.2192 24.5519 10.1821 25.421 9.17305 25.9815C8.44419 26.4021 7.74396 26.6264 7.04341 26.7101C6.32114 26.7908 5.59009 26.6947 4.9132 26.4301C3.54012 25.8695 2.55901 24.6079 2.36303 23.1505C2.27902 22.45 2.33475 21.7494 2.61532 20.9646C2.699 20.684 2.83957 20.4041 2.97957 20.0678C3.17587 19.6192 3.39955 19.1424 3.6241 18.6661L3.65238 18.6104C5.58604 14.434 7.65995 10.1739 9.81812 6.02606L9.90213 5.8575C10.1267 5.43752 10.351 4.98897 10.575 4.56844C10.7993 4.11985 11.0512 3.69929 11.3598 3.33475C11.9484 2.66277 12.7329 2.29794 13.602 2.29794C14.4711 2.29794 15.2556 2.66277 15.8442 3.33475C16.1528 3.70016 16.4048 4.12068 16.629 4.56841C16.8536 4.98897 17.0778 5.43752 17.3016 5.8575L17.3858 6.02606C19.5111 10.1911 21.5575 14.396 23.5239 18.6384V18.6664C23.7484 19.1155 23.9444 19.6195 24.1687 20.0686C24.3087 20.4043 24.449 20.6843 24.5329 20.9649C24.7567 21.6929 24.8407 22.394 24.7284 23.1225L24.7284 23.1226Z'
        fill='currentColor'
      />
    </svg>
  )
}

export default Airbnb
