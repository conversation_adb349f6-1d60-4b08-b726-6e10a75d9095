// React Imports
import type { SVGAttributes } from 'react'

const Coinbase = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg width='107' height='39' viewBox='0 0 107 39' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <g id='logo-4'>
        <path
          id='Vector'
          d='M13.4571 29.7984C10.4234 29.7984 7.45386 27.6145 7.45386 22.6845C7.45386 17.7385 10.4234 15.5866 13.4571 15.5866C14.9499 15.5866 16.1217 15.972 16.9563 16.518L16.0414 18.5254C15.4796 18.1239 14.6449 17.867 13.8102 17.867C11.9804 17.867 10.311 19.3122 10.311 22.6524C10.311 25.9926 12.0285 27.4699 13.8102 27.4699C14.6449 27.4699 15.4796 27.213 16.0414 26.8115L16.9563 28.867C16.0895 29.4452 14.9499 29.7984 13.4571 29.7984ZM23.9066 29.7984C20.0382 29.7984 17.9034 26.7312 17.9034 22.6845C17.9034 18.6377 20.0221 15.5866 23.9066 15.5866C27.775 15.5866 29.9098 18.6217 29.9098 22.6845C29.9098 26.7312 27.775 29.7984 23.9066 29.7984ZM23.9066 17.7866C21.7557 17.7866 20.6963 19.7137 20.6963 22.6524C20.6963 25.5911 21.7557 27.5342 23.9066 27.5342C26.0575 27.5342 27.1169 25.5911 27.1169 22.6524C27.1169 19.7137 26.0575 17.7866 23.9066 17.7866ZM33.6338 13.3866C32.7188 13.3866 31.9805 12.68 31.9805 11.8129C31.9805 10.9457 32.7188 10.2391 33.6338 10.2391C34.5487 10.2391 35.287 10.9457 35.287 11.8129C35.287 12.68 34.5326 13.3866 33.6338 13.3866ZM32.2373 15.8596H35.0302V29.5094H32.2373V15.8596ZM45.9773 29.5094V20.4042C45.9773 18.8144 45.0142 17.8188 43.1202 17.8188C42.1089 17.8188 41.1779 17.9954 40.6161 18.2202V29.5254H37.8553V16.5501C39.2197 15.9881 40.9693 15.5866 43.1041 15.5866C46.9244 15.5866 48.7703 17.2567 48.7703 20.1472V29.5255H45.9773V29.5094ZM56.1058 29.7984C54.3401 29.7984 52.5905 29.3649 51.5151 28.8349V9.62891H54.2759V16.2129C54.934 15.9078 55.9934 15.6509 56.9404 15.6509C60.4557 15.6509 62.8474 18.1881 62.8474 22.3633C62.8474 27.5181 60.1828 29.7984 56.1058 29.7984ZM56.4589 17.8188C55.7045 17.8188 54.8056 17.9954 54.2759 18.2684V27.2291C54.6772 27.4057 55.4637 27.5824 56.2502 27.5824C58.4493 27.5824 60.0705 26.0568 60.0705 22.5721C60.0865 19.5852 58.674 17.8188 56.4589 17.8188ZM70.3595 29.7984C66.4429 29.7984 64.4525 28.2086 64.4525 25.5108C64.4525 21.7049 68.4975 21.0305 72.6227 20.8057V19.9385C72.6227 18.2202 71.4831 17.61 69.7334 17.61C68.4493 17.61 66.8763 18.0115 65.9614 18.4451L65.2551 16.5501C66.3466 16.0684 68.1925 15.5866 70.0224 15.5866C73.2808 15.5866 75.2712 16.8552 75.2712 20.2275V28.8349C74.2921 29.3649 72.2857 29.7984 70.3595 29.7984ZM72.6387 22.6524C69.8458 22.7969 67.0689 23.0378 67.0689 25.4626C67.0689 26.9079 68.1764 27.7911 70.2792 27.7911C71.162 27.7911 72.2054 27.6466 72.6387 27.4378V22.6524ZM81.4991 29.7984C79.91 29.7984 78.2407 29.3649 77.2455 28.8349L78.1765 26.7152C78.8828 27.1488 80.3756 27.5984 81.4189 27.5984C82.9117 27.5984 83.9069 26.8597 83.9069 25.7195C83.9069 24.4831 82.8635 24.0013 81.4831 23.4874C79.6533 22.7969 77.6147 21.9619 77.6147 19.4086C77.6147 17.1604 79.3643 15.5866 82.398 15.5866C84.0513 15.5866 85.4157 15.9881 86.3788 16.5501L85.512 18.4772C84.902 18.0918 83.6822 17.6742 82.703 17.6742C81.2584 17.6742 80.4558 18.429 80.4558 19.4246C80.4558 20.6611 81.4671 21.0947 82.8154 21.6086C84.7094 22.3152 86.8122 23.102 86.8122 25.7677C86.7961 28.1925 84.9181 29.7984 81.4991 29.7984ZM100.263 22.6203L91.1942 23.8889C91.4671 26.3458 93.0722 27.5824 95.3676 27.5824C96.732 27.5824 98.2087 27.2451 99.1397 26.7473L99.9423 28.8189C98.8829 29.3809 97.053 29.7824 95.175 29.7824C90.8732 29.7824 88.4655 27.0203 88.4655 22.6684C88.4655 18.4932 90.7929 15.5706 94.6132 15.5706C98.1606 15.5706 100.263 17.8991 100.263 21.5765C100.295 21.9137 100.295 22.267 100.263 22.6203ZM94.5971 17.61C92.4783 17.61 91.0819 19.2319 91.0337 22.0743L97.6469 21.159C97.6148 18.7823 96.4109 17.61 94.5971 17.61Z'
          fill='currentColor'
        />
      </g>
    </svg>
  )
}

export default Coinbase
