// React Imports
import type { SVGAttributes } from 'react'

const Paper = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='64' height='65' viewBox='0 0 64 65' fill='none' {...props}>
      <path
        opacity='0.2'
        d='M52.575 9.66926L5.97499 22.7943C5.57831 22.9027 5.2247 23.1308 4.96234 23.4475C4.69997 23.7642 4.54161 24.154 4.50881 24.564C4.47602 24.9739 4.57039 25.384 4.77907 25.7383C4.98775 26.0927 5.3006 26.3741 5.67499 26.5443L27.075 36.6693C27.4942 36.8634 27.8309 37.2001 28.025 37.6193L38.15 59.0193C38.3201 59.3937 38.6016 59.7065 38.9559 59.9152C39.3103 60.1239 39.7204 60.2182 40.1303 60.1854C40.5402 60.1526 40.9301 59.9943 41.2468 59.7319C41.5634 59.4695 41.7915 59.1159 41.9 58.7193L55.025 12.1193C55.1245 11.7792 55.1306 11.4186 55.0428 11.0754C54.955 10.7321 54.7765 10.4188 54.5259 10.1683C54.2754 9.91778 53.9621 9.73925 53.6189 9.65145C53.2756 9.56365 52.9151 9.5698 52.575 9.66926Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M53.8666 8.68265C53.3513 8.55084 52.8102 8.55959 52.2995 8.70791L52.2942 8.70947L5.71115 21.8297L5.70701 21.8308C5.11366 21.9939 4.5848 22.3356 4.19216 22.8095C3.79862 23.2845 3.56107 23.8693 3.51188 24.4842C3.46268 25.0991 3.60424 25.7142 3.91726 26.2458C4.22884 26.7749 4.69522 27.1955 5.25338 27.4511L26.6472 37.5732L26.6472 37.5732L26.6546 37.5767C26.8589 37.6712 27.0229 37.8353 27.1175 38.0395L27.1174 38.0395L27.1209 38.0469L37.243 59.4407C37.4985 59.9989 37.9192 60.4653 38.4484 60.7769C38.9799 61.0899 39.595 61.2314 40.2099 61.1822C40.8248 61.1331 41.4096 60.8955 41.8846 60.502C42.3586 60.1093 42.7002 59.5804 42.8634 58.9871L42.8645 58.983L55.9847 12.4L55.9862 12.3948C56.1346 11.8841 56.1433 11.3429 56.0115 10.8276C55.8792 10.3105 55.6103 9.83858 55.2329 9.4612C54.8556 9.08382 54.3836 8.81491 53.8666 8.68265ZM52.846 10.6318L52.5749 9.66926L52.8556 10.629C53.0235 10.5799 53.2015 10.5769 53.3709 10.6203C53.5404 10.6636 53.695 10.7517 53.8187 10.8754C53.9424 10.9991 54.0305 11.1538 54.0739 11.3232C54.1172 11.4927 54.1142 11.6707 54.0651 11.8385L54.065 11.8385L54.0623 11.8482L40.9373 58.4482L40.9353 58.4555C40.8811 58.6539 40.767 58.8307 40.6087 58.9619C40.4503 59.093 40.2554 59.1722 40.0504 59.1886C39.8455 59.205 39.6404 59.1578 39.4632 59.0535C39.2861 58.9492 39.1454 58.7927 39.0603 58.6055L39.0538 58.5916L28.9323 37.199L28.9303 37.1948C28.9285 37.191 28.9268 37.1872 28.925 37.1834L39.732 26.3764C40.1225 25.9858 40.1225 25.3527 39.732 24.9621C39.3415 24.5716 38.7083 24.5716 38.3178 24.9621L27.5108 35.7691C27.5069 35.7673 27.503 35.7655 27.4991 35.7637L6.10255 25.6403L6.0886 25.6339C5.9014 25.5488 5.74498 25.4081 5.64064 25.2309C5.53629 25.0537 5.48911 24.8487 5.50551 24.6437C5.5219 24.4387 5.60109 24.2438 5.73227 24.0855C5.86345 23.9271 6.04025 23.8131 6.2386 23.7589L6.2386 23.7589L6.24598 23.7568L52.846 10.6318Z'
        fill='currentColor'
      />
    </svg>
  )
}

export default Paper
