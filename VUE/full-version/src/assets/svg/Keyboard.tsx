// React Imports
import type { SVGAttributes } from 'react'

const Keyboard = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40' fill='none' {...props}>
      <path
        opacity='0.2'
        d='M34.9219 8.75H5.07812C4.34462 8.75 3.75 9.34462 3.75 10.0781V29.9219C3.75 30.6554 4.34462 31.25 5.07812 31.25H34.9219C35.6554 31.25 36.25 30.6554 36.25 29.9219V10.0781C36.25 9.34462 35.6554 8.75 34.9219 8.75Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.75 10.0781C4.75 9.89691 4.89691 9.75 5.07812 9.75H34.9219C35.1031 9.75 35.25 9.89691 35.25 10.0781V29.9219C35.25 30.1031 35.1031 30.25 34.9219 30.25H5.07812C4.89691 30.25 4.75 30.1031 4.75 29.9219V10.0781ZM5.07812 7.75C3.79234 7.75 2.75 8.79234 2.75 10.0781V29.9219C2.75 31.2077 3.79234 32.25 5.07812 32.25H34.9219C36.2077 32.25 37.25 31.2077 37.25 29.9219V10.0781C37.25 8.79234 36.2077 7.75 34.9219 7.75H5.07812ZM8.75 14C8.19772 14 7.75 14.4477 7.75 15C7.75 15.5523 8.19772 16 8.75 16H31.25C31.8023 16 32.25 15.5523 32.25 15C32.25 14.4477 31.8023 14 31.25 14H8.75ZM8.75 19C8.19772 19 7.75 19.4477 7.75 20C7.75 20.5523 8.19772 21 8.75 21H31.25C31.8023 21 32.25 20.5523 32.25 20C32.25 19.4477 31.8023 19 31.25 19H8.75ZM7.75 25C7.75 24.4477 8.19772 24 8.75 24H10C10.5523 24 11 24.4477 11 25C11 25.5523 10.5523 26 10 26H8.75C8.19772 26 7.75 25.5523 7.75 25ZM15 24C14.4477 24 14 24.4477 14 25C14 25.5523 14.4477 26 15 26H25C25.5523 26 26 25.5523 26 25C26 24.4477 25.5523 24 25 24H15ZM29 25C29 24.4477 29.4477 24 30 24H31.25C31.8023 24 32.25 24.4477 32.25 25C32.25 25.5523 31.8023 26 31.25 26H30C29.4477 26 29 25.5523 29 25Z'
        fill='currentColor'
      />
    </svg>
  )
}

export default Keyboard
