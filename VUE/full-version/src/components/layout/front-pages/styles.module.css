.header {
  position: sticky;
  inset-block-start: 0;
  z-index: var(--header-z-index);
  display: flex;
  align-items: center;
  justify-content: center;
  inline-size: 100%;
  pointer-events: none;

  &:before {
    content: '';
    position: absolute;
    z-index: -1;
    inset-block-start: 0;
    inset-inline: 0;
    block-size: 100%;
    backdrop-filter: saturate(100%) blur(6px);
  }

  .navbar {
    inline-size: calc(100% - 48px);
    padding-block: 0.438rem;
    padding-inline: 1.5rem;
    margin-inline: auto;
    margin-block-start: 1rem;
    pointer-events: auto;
    background-color: rgb(var(--mui-palette-background-paperChannel) / 0.38);
    border: 2px solid rgb(var(--mui-palette-background-paperChannel) / 0.68);
    border-radius: var(--mui-shape-borderRadius);

    &.headerScrolled {
      background-color: var(--mui-palette-background-paper);
      border-color: var(--mui-palette-background-paper);
      box-shadow: var(--mui-customShadows-sm);
    }

    @media (min-width: 600px) {
      padding-inline: 2rem;
    }
    @media (min-width: 900px) {
      max-inline-size: calc(900px - 48px);
    }
    @media (min-width: 1200px) {
      max-inline-size: calc(1200px - 48px);
    }
    @media (min-width: 1920px) {
      max-inline-size: calc(1440px - 48px);
    }

    .navbarContent {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 1.5rem;
    }
  }
}

.inputBorder {
  fieldset {
    border-color: rgb(var(--mui-mainColorChannels-dark) / 0.22) !important;
  }
  label,
  input {
    color: rgb(var(--mui-mainColorChannels-dark) / 0.9) !important;
  }
}

.footerRadius {
  border-start-start-radius: 3.75rem;
  border-start-end-radius: 3.75rem;
}
