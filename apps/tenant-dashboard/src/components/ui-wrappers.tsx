"use client";

// Этот файл содержит клиентские обертки для компонентов UI
// Это позволяет использовать компоненты UI в серверных компонентах Next.js

// Импортируем компоненты напрямую из исходных файлов, а не из скомпилированного пакета
import { StatCard } from '@pactcrm/ui/src/components/dashboard/stat-card';
import { HorizontalStatCard } from '@pactcrm/ui/src/components/dashboard/horizontal-stat-card';
import { StatCardWithBorder } from '@pactcrm/ui/src/components/dashboard/stat-card-with-border';
import { KPICard } from '@pactcrm/ui/src/components/dashboard/kpi-card';
import { ProgressCard } from '@pactcrm/ui/src/components/dashboard/progress-card';
import { ComparisonCard } from '@pactcrm/ui/src/components/dashboard/comparison-card';
import { ChartCard } from '@pactcrm/ui/src/components/dashboard/chart-card';
import { DataTable } from '@pactcrm/ui/src/components/dashboard/data-table';
import { TaskList } from '@pactcrm/ui/src/components/dashboard/task-list';
import { MainLayout } from '@pactcrm/ui/src/components/layout/main-layout';

export {
  StatCard,
  HorizontalStatCard,
  StatCardWithBorder,
  KPICard,
  ProgressCard,
  ComparisonCard,
  ChartCard,
  DataTable,
  TaskList,
  MainLayout
};
