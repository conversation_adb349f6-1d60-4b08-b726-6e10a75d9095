'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Login03 } from '../../components/ui-wrappers'

/**
 * Страница авторизации для TenantDashboard
 *
 * Проверяет роль пользователя после успешной авторизации и перенаправляет только
 * пользователей с ролями tenant_admin или after_sales_manager. Для других пользователей
 * показывает ошибку и выполняет выход из системы.
 *
 * @returns Компонент страницы авторизации TenantDashboard
 */
export default function TenantLoginPage() {
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const supabase = createClientComponentClient()

  const handleLogin = async (data: { email: string; password: string }) => {
    setLoading(true)
    setError(null)

    try {
      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      })

      if (error) {
        throw error
      }

      // Проверяем роль пользователя
      const role = authData.user?.user_metadata?.role

      if (role !== 'tenant_admin' && role !== 'after_sales_manager') {
        // Если пользователь не tenant_admin или after_sales_manager, выходим из системы
        await supabase.auth.signOut()
        setError('У вас нет доступа к панели застройщика')
        setLoading(false)
        return
      }

      // Перенаправляем на панель застройщика
      router.push('/dashboard')
    } catch (error: any) {
      setError(error.message || 'Произошла ошибка при входе')
    } finally {
      setLoading(false)
    }
  }

  const handleForgotPassword = () => {
    router.push('/forgot-password')
  }

  return (
    <Login03
      onSubmit={handleLogin}
      loading={loading}
      error={error}
      title="Панель застройщика"
      subtitle="Войдите в систему управления недвижимостью"
      companyName="PactCRM"
      showForgotPassword={true}
      onForgotPassword={handleForgotPassword}
    />
  )
}
