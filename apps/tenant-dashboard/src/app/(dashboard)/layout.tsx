"use client";

import React from 'react';
import { MainLayout } from '@/components/ui-wrappers';
import { Building, Home, Users, FileText, CreditCard, Settings, Bell, HelpCircle } from 'lucide-react';
import Image from 'next/image';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const sidebarItems = [
    {
      title: 'Дашборд',
      href: '/dashboard',
      icon: <Home className="h-4 w-4" />,
      isActive: true,
    },
    {
      title: 'Объекты',
      href: '/properties',
      icon: <Building className="h-4 w-4" />,
      submenu: [
        {
          title: 'Все объекты',
          href: '/properties',
        },
        {
          title: 'Жилые комплексы',
          href: '/properties/complexes',
        },
        {
          title: 'Здания',
          href: '/properties/buildings',
        },
        {
          title: 'Квартиры',
          href: '/properties/apartments',
        },
      ],
    },
    {
      title: 'Клиенты',
      href: '/clients',
      icon: <Users className="h-4 w-4" />,
    },
    {
      title: 'Договоры',
      href: '/contracts',
      icon: <FileText className="h-4 w-4" />,
      submenu: [
        {
          title: 'Все договоры',
          href: '/contracts',
        },
        {
          title: 'Шаблоны договоров',
          href: '/contract-templates',
        },
      ],
    },
    {
      title: 'Платежи',
      href: '/payments',
      icon: <CreditCard className="h-4 w-4" />,
    },
    {
      title: 'Настройки',
      href: '/settings',
      icon: <Settings className="h-4 w-4" />,
    },
    {
      title: 'Демо компонентов',
      href: '/components-demo',
      icon: <Building className="h-4 w-4" />,
    },
  ];

  const userNavigation = {
    name: 'Иван Петров',
    email: '<EMAIL>',
    initials: 'ИП',
    items: [
      {
        label: 'Профиль',
        href: '/profile',
      },
      {
        label: 'Настройки',
        href: '/settings',
      },
      {
        label: 'Выйти',
        onClick: () => console.log('Выход из системы'),
      },
    ],
  };

  const headerActions = (
    <div className="flex items-center gap-2">
      <button className="rounded-full p-2 hover:bg-accent">
        <Bell className="h-5 w-5" />
      </button>
      <button className="rounded-full p-2 hover:bg-accent">
        <HelpCircle className="h-5 w-5" />
      </button>
    </div>
  );

  const logo = (
    <div className="flex items-center gap-2">
      <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary">
        <span className="text-lg font-bold text-primary-foreground">P</span>
      </div>
      <span className="font-semibold">PactCRM</span>
    </div>
  );

  return (
    <MainLayout
      sidebarItems={sidebarItems}
      logo={logo}
      userNavigation={userNavigation}
      headerActions={headerActions}
      onSearch={(value) => console.log('Поиск:', value)}
    >
      {children}
    </MainLayout>
  );
}
