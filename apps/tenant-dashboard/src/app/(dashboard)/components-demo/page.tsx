"use client";

import React from 'react';
import {
  StatCard,
  HorizontalStatCard,
  StatCardWithBorder,
  KPICard,
  ProgressCard,
  ComparisonCard,
  AdvancedDataTable,
  PropertyTable,
  ClientTable,
  PaymentTable,
  ContractTable,
  Login03
} from '@pactcrm/ui';
import {
  Building,
  Users,
  FileText,
  CreditCard,
  TrendingUp,
  Target,
  BarChart3,
  PieChart
} from 'lucide-react';

export default function ComponentsDemoPage() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold">Демонстрация новых компонентов</h1>
        <p className="text-muted-foreground mt-2">
          Фаза 1: Расширенные статистические карточки
        </p>
      </div>

      {/* Улучшенный StatCard */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Улучшенный StatCard</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Базовый вариант"
            value="245"
            icon={<Building className="h-4 w-4" />}
            description="За последний месяц"
            trend={{ value: 12, isPositive: true }}
          />
          <StatCard
            title="С границей"
            value="128"
            icon={<Users className="h-4 w-4" />}
            variant="bordered"
            color="success"
            description="Активные клиенты"
            trend={{ value: 8, isPositive: true, period: "неделя" }}
          />
          <StatCard
            title="Градиент"
            value="₽12.4M"
            icon={<CreditCard className="h-4 w-4" />}
            variant="gradient"
            color="primary"
            subtitle="Общая выручка"
            description="За текущий месяц"
            trend={{ value: 15, isPositive: true }}
          />
          <StatCard
            title="Анимированная"
            value="86"
            icon={<FileText className="h-4 w-4" />}
            variant="bordered"
            color="warning"
            animated={true}
            description="Активные договоры"
            trend={{ value: 3, isPositive: false }}
          />
        </div>
      </section>

      {/* HorizontalStatCard */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">HorizontalStatCard</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <HorizontalStatCard
            title="Продажи недвижимости"
            value="₽8.2M"
            icon={<Building className="h-4 w-4" />}
            description="За текущий месяц"
            trend={{ value: 12.5, isPositive: true }}
            color="primary"
            iconPosition="left"
          />
          <HorizontalStatCard
            title="Новые клиенты"
            value="47"
            icon={<Users className="h-4 w-4" />}
            description="За последнюю неделю"
            trend={{ value: 8.3, isPositive: true }}
            color="success"
            iconPosition="right"
            animated={true}
          />
        </div>
      </section>

      {/* StatCardWithBorder */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">StatCardWithBorder</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCardWithBorder
            title="Левая граница"
            value="₽2.1M"
            icon={<TrendingUp className="h-4 w-4" />}
            description="Прибыль за месяц"
            trend={{ value: 18.2, isPositive: true }}
            borderPosition="left"
            color="success"
          />
          <StatCardWithBorder
            title="Верхняя граница"
            value="156"
            icon={<Target className="h-4 w-4" />}
            description="Выполненные цели"
            trend={{ value: 5.7, isPositive: false }}
            borderPosition="top"
            color="warning"
          />
          <StatCardWithBorder
            title="Толстая граница"
            value="92%"
            icon={<BarChart3 className="h-4 w-4" />}
            description="Заполненность"
            borderWidth="thick"
            color="info"
          />
          <StatCardWithBorder
            title="Без hover эффекта"
            value="34"
            icon={<PieChart className="h-4 w-4" />}
            description="Новые объекты"
            hoverEffect={false}
            color="error"
          />
        </div>
      </section>

      {/* KPICard */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">KPICard</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <KPICard
            title="План продаж"
            current={2450000}
            target={3000000}
            unit="₽"
            period="месяц"
            trend={{ value: 12.5, isPositive: true, period: "неделя" }}
            status="good"
            icon={<Target className="h-4 w-4" />}
          />
          <KPICard
            title="Заполненность объектов"
            current={234}
            target={250}
            unit=" квартир"
            period="квартал"
            status="excellent"
            icon={<Building className="h-4 w-4" />}
            animated={true}
          />
          <KPICard
            title="Привлечение клиентов"
            current={45}
            target={100}
            unit=" клиентов"
            period="месяц"
            trend={{ value: 8.3, isPositive: false }}
            status="warning"
            icon={<Users className="h-4 w-4" />}
          />
        </div>
      </section>

      {/* ProgressCard */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">ProgressCard</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <ProgressCard
            title="Строительство ЖК Солнечный"
            value={234}
            maxValue={300}
            unit=" квартир"
            description="Готовность к сдаче"
            progress={{
              percentage: 78,
              color: "bg-blue-500",
              animated: true
            }}
            segments={[
              { label: "Готовые", value: 180, color: "bg-green-500" },
              { label: "В процессе", value: 54, color: "bg-yellow-500" },
              { label: "Планируемые", value: 66, color: "bg-gray-300" }
            ]}
            icon={<Building className="h-4 w-4" />}
          />
          <ProgressCard
            title="Сбор платежей"
            value={8200000}
            maxValue={12000000}
            unit="₽"
            description="За текущий месяц"
            progress={{
              percentage: 68.3,
              color: "bg-green-500",
              gradient: true
            }}
            icon={<CreditCard className="h-4 w-4" />}
            animated={true}
          />
        </div>
      </section>

      {/* ComparisonCard */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">ComparisonCard</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <ComparisonCard
            title="Продажи"
            current={{
              label: "Этот месяц",
              value: 2450000,
              period: "май 2024"
            }}
            previous={{
              label: "Прошлый месяц",
              value: 1980000,
              period: "апр 2024"
            }}
            unit="₽"
            comparison={{
              type: "percentage",
              isPositive: true,
              value: 23.7
            }}
            chart={{
              data: [1800000, 1950000, 2100000, 1980000, 2450000],
              type: "line",
              color: "bg-green-500"
            }}
            icon={<TrendingUp className="h-4 w-4" />}
          />
          <ComparisonCard
            title="Новые клиенты"
            current={{
              label: "Эта неделя",
              value: 47
            }}
            previous={{
              label: "Прошлая неделя",
              value: 52
            }}
            unit=" клиентов"
            comparison={{
              type: "absolute",
              isPositive: false,
              value: 5
            }}
            chart={{
              data: [45, 52, 48, 52, 47],
              type: "bar",
              color: "bg-blue-500"
            }}
            icon={<Users className="h-4 w-4" />}
            animated={true}
          />
          <ComparisonCard
            title="Средний чек"
            current={{
              label: "Текущий",
              value: 1250000
            }}
            previous={{
              label: "Предыдущий",
              value: 1180000
            }}
            unit="₽"
            comparison={{
              type: "percentage",
              isPositive: true,
              value: 5.9
            }}
            icon={<BarChart3 className="h-4 w-4" />}
          />
        </div>
      </section>

      {/* Loading состояния */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Loading состояния</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title=""
            value=""
            loading={true}
          />
          <HorizontalStatCard
            title=""
            value=""
            loading={true}
          />
          <KPICard
            title=""
            current={0}
            target={0}
            loading={true}
          />
          <ComparisonCard
            title=""
            current={{ label: "", value: 0 }}
            previous={{ label: "", value: 0 }}
            comparison={{ type: "percentage", isPositive: true, value: 0 }}
            loading={true}
          />
        </div>
      </section>

      {/* Фаза 2: Продвинутые таблицы */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Фаза 2: Продвинутые таблицы</h2>

        {/* AdvancedDataTable */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">AdvancedDataTable</h3>
          <AdvancedDataTable
            columns={[
              {
                accessorKey: 'name',
                header: 'Название',
              },
              {
                accessorKey: 'status',
                header: 'Статус',
              },
              {
                accessorKey: 'value',
                header: 'Значение',
              },
            ]}
            data={[
              { name: 'Элемент 1', status: 'Активный', value: 100 },
              { name: 'Элемент 2', status: 'Неактивный', value: 200 },
              { name: 'Элемент 3', status: 'Активный', value: 300 },
            ]}
            enableRowSelection={true}
            enableExport={true}
            onExport={(data, format) => console.log(`Экспорт ${data.length} записей в ${format}`)}
          />
        </div>

        {/* PropertyTable */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">PropertyTable</h3>
          <PropertyTable
            properties={[
              {
                id: '1',
                name: 'ЖК Солнечный',
                address: 'ул. Ленина, 123',
                type: 'apartment',
                status: 'construction',
                totalUnits: 300,
                availableUnits: 234,
                priceFrom: 2500000,
                priceTo: 8500000,
                completionDate: '2024-12-31',
                developer: 'СтройИнвест',
                createdAt: '2024-01-15',
                updatedAt: '2024-01-27',
              },
              {
                id: '2',
                name: 'Бизнес-центр Альфа',
                address: 'пр. Мира, 45',
                type: 'commercial',
                status: 'ready',
                totalUnits: 50,
                availableUnits: 12,
                priceFrom: 15000000,
                priceTo: 45000000,
                completionDate: '2024-06-30',
                developer: 'КоммерцСтрой',
                createdAt: '2023-08-10',
                updatedAt: '2024-01-20',
              },
            ]}
            onPropertyView={(property) => console.log('Просмотр объекта:', property)}
            onPropertyEdit={(property) => console.log('Редактирование объекта:', property)}
          />
        </div>

        {/* ClientTable */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">ClientTable</h3>
          <ClientTable
            clients={[
              {
                id: '1',
                firstName: 'Иван',
                lastName: 'Петров',
                email: '<EMAIL>',
                phone: '+7 (999) 123-45-67',
                status: 'client',
                source: 'website',
                totalPurchases: 2500000,
                lastActivity: '2024-01-25',
                registrationDate: '2023-12-01',
              },
              {
                id: '2',
                firstName: 'Мария',
                lastName: 'Сидорова',
                email: '<EMAIL>',
                phone: '+7 (999) 987-65-43',
                status: 'prospect',
                source: 'referral',
                totalPurchases: 0,
                lastActivity: '2024-01-26',
                registrationDate: '2024-01-15',
              },
            ]}
            onClientView={(client) => console.log('Просмотр клиента:', client)}
            onClientMessage={(client) => console.log('Сообщение клиенту:', client)}
          />
        </div>
      </section>

      {/* Современная страница авторизации */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Современная страница авторизации</h2>
        <div className="border rounded-lg p-4 bg-gray-50">
          <p className="text-sm text-muted-foreground mb-4">
            Компонент Login03 - современная страница авторизации в стиле shadcn/ui
          </p>
          <div className="max-w-md mx-auto">
            <Login03
              onSubmit={async (data) => {
                console.log('Данные авторизации:', data);
                // Имитация задержки
                await new Promise(resolve => setTimeout(resolve, 1000));
              }}
              title="Демо авторизации"
              subtitle="Пример современной формы входа"
              companyName="PactCRM"
              showForgotPassword={true}
              showSignUp={true}
              onForgotPassword={() => console.log('Забыли пароль')}
              onSignUp={() => console.log('Регистрация')}
            />
          </div>
        </div>
      </section>
    </div>
  );
}
