"use client";

import React from 'react';
import {
  StatCard,
  HorizontalStatCard,
  StatCardWithBorder,
  KPICard,
  ProgressCard,
  ComparisonCard,
  AdvancedDataTable,
  PropertyTable,
  ClientTable,
  PaymentTable,
  ContractTable,
  Login03,
  LineChart,
  BarChart,
  <PERSON><PERSON>hart,
  AreaChart,
  DonutChart,
  RadarChart,
  SalesChart,
  PaymentChart,
  ClientActivityChart,
  PropertyStatsChart
} from '@pactcrm/ui';
import {
  Building,
  Users,
  FileText,
  CreditCard,
  TrendingUp,
  Target,
  BarChart3,
  PieChart
} from 'lucide-react';

export default function ComponentsDemoPage() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold">Демонстрация новых компонентов</h1>
        <p className="text-muted-foreground mt-2">
          Фаза 1: Расширенные статистические карточки
        </p>
      </div>

      {/* Улучшенный StatCard */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Улучшенный StatCard</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Базовый вариант"
            value="245"
            icon={<Building className="h-4 w-4" />}
            description="За последний месяц"
            trend={{ value: 12, isPositive: true }}
          />
          <StatCard
            title="С границей"
            value="128"
            icon={<Users className="h-4 w-4" />}
            variant="bordered"
            color="success"
            description="Активные клиенты"
            trend={{ value: 8, isPositive: true, period: "неделя" }}
          />
          <StatCard
            title="Градиент"
            value="₽12.4M"
            icon={<CreditCard className="h-4 w-4" />}
            variant="gradient"
            color="primary"
            subtitle="Общая выручка"
            description="За текущий месяц"
            trend={{ value: 15, isPositive: true }}
          />
          <StatCard
            title="Анимированная"
            value="86"
            icon={<FileText className="h-4 w-4" />}
            variant="bordered"
            color="warning"
            animated={true}
            description="Активные договоры"
            trend={{ value: 3, isPositive: false }}
          />
        </div>
      </section>

      {/* HorizontalStatCard */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">HorizontalStatCard</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <HorizontalStatCard
            title="Продажи недвижимости"
            value="₽8.2M"
            icon={<Building className="h-4 w-4" />}
            description="За текущий месяц"
            trend={{ value: 12.5, isPositive: true }}
            color="primary"
            iconPosition="left"
          />
          <HorizontalStatCard
            title="Новые клиенты"
            value="47"
            icon={<Users className="h-4 w-4" />}
            description="За последнюю неделю"
            trend={{ value: 8.3, isPositive: true }}
            color="success"
            iconPosition="right"
            animated={true}
          />
        </div>
      </section>

      {/* StatCardWithBorder */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">StatCardWithBorder</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCardWithBorder
            title="Левая граница"
            value="₽2.1M"
            icon={<TrendingUp className="h-4 w-4" />}
            description="Прибыль за месяц"
            trend={{ value: 18.2, isPositive: true }}
            borderPosition="left"
            color="success"
          />
          <StatCardWithBorder
            title="Верхняя граница"
            value="156"
            icon={<Target className="h-4 w-4" />}
            description="Выполненные цели"
            trend={{ value: 5.7, isPositive: false }}
            borderPosition="top"
            color="warning"
          />
          <StatCardWithBorder
            title="Толстая граница"
            value="92%"
            icon={<BarChart3 className="h-4 w-4" />}
            description="Заполненность"
            borderWidth="thick"
            color="info"
          />
          <StatCardWithBorder
            title="Без hover эффекта"
            value="34"
            icon={<PieChart className="h-4 w-4" />}
            description="Новые объекты"
            hoverEffect={false}
            color="error"
          />
        </div>
      </section>

      {/* KPICard */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">KPICard</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <KPICard
            title="План продаж"
            current={2450000}
            target={3000000}
            unit="₽"
            period="месяц"
            trend={{ value: 12.5, isPositive: true, period: "неделя" }}
            status="good"
            icon={<Target className="h-4 w-4" />}
          />
          <KPICard
            title="Заполненность объектов"
            current={234}
            target={250}
            unit=" квартир"
            period="квартал"
            status="excellent"
            icon={<Building className="h-4 w-4" />}
            animated={true}
          />
          <KPICard
            title="Привлечение клиентов"
            current={45}
            target={100}
            unit=" клиентов"
            period="месяц"
            trend={{ value: 8.3, isPositive: false }}
            status="warning"
            icon={<Users className="h-4 w-4" />}
          />
        </div>
      </section>

      {/* ProgressCard */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">ProgressCard</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <ProgressCard
            title="Строительство ЖК Солнечный"
            value={234}
            maxValue={300}
            unit=" квартир"
            description="Готовность к сдаче"
            progress={{
              percentage: 78,
              color: "bg-blue-500",
              animated: true
            }}
            segments={[
              { label: "Готовые", value: 180, color: "bg-green-500" },
              { label: "В процессе", value: 54, color: "bg-yellow-500" },
              { label: "Планируемые", value: 66, color: "bg-gray-300" }
            ]}
            icon={<Building className="h-4 w-4" />}
          />
          <ProgressCard
            title="Сбор платежей"
            value={8200000}
            maxValue={12000000}
            unit="₽"
            description="За текущий месяц"
            progress={{
              percentage: 68.3,
              color: "bg-green-500",
              gradient: true
            }}
            icon={<CreditCard className="h-4 w-4" />}
            animated={true}
          />
        </div>
      </section>

      {/* ComparisonCard */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">ComparisonCard</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <ComparisonCard
            title="Продажи"
            current={{
              label: "Этот месяц",
              value: 2450000,
              period: "май 2024"
            }}
            previous={{
              label: "Прошлый месяц",
              value: 1980000,
              period: "апр 2024"
            }}
            unit="₽"
            comparison={{
              type: "percentage",
              isPositive: true,
              value: 23.7
            }}
            chart={{
              data: [1800000, 1950000, 2100000, 1980000, 2450000],
              type: "line",
              color: "bg-green-500"
            }}
            icon={<TrendingUp className="h-4 w-4" />}
          />
          <ComparisonCard
            title="Новые клиенты"
            current={{
              label: "Эта неделя",
              value: 47
            }}
            previous={{
              label: "Прошлая неделя",
              value: 52
            }}
            unit=" клиентов"
            comparison={{
              type: "absolute",
              isPositive: false,
              value: 5
            }}
            chart={{
              data: [45, 52, 48, 52, 47],
              type: "bar",
              color: "bg-blue-500"
            }}
            icon={<Users className="h-4 w-4" />}
            animated={true}
          />
          <ComparisonCard
            title="Средний чек"
            current={{
              label: "Текущий",
              value: 1250000
            }}
            previous={{
              label: "Предыдущий",
              value: 1180000
            }}
            unit="₽"
            comparison={{
              type: "percentage",
              isPositive: true,
              value: 5.9
            }}
            icon={<BarChart3 className="h-4 w-4" />}
          />
        </div>
      </section>

      {/* Loading состояния */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Loading состояния</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title=""
            value=""
            loading={true}
          />
          <HorizontalStatCard
            title=""
            value=""
            loading={true}
          />
          <KPICard
            title=""
            current={0}
            target={0}
            loading={true}
          />
          <ComparisonCard
            title=""
            current={{ label: "", value: 0 }}
            previous={{ label: "", value: 0 }}
            comparison={{ type: "percentage", isPositive: true, value: 0 }}
            loading={true}
          />
        </div>
      </section>

      {/* Фаза 2: Продвинутые таблицы */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Фаза 2: Продвинутые таблицы</h2>

        {/* AdvancedDataTable */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">AdvancedDataTable</h3>
          <AdvancedDataTable
            columns={[
              {
                accessorKey: 'name',
                header: 'Название',
              },
              {
                accessorKey: 'status',
                header: 'Статус',
              },
              {
                accessorKey: 'value',
                header: 'Значение',
              },
            ]}
            data={[
              { name: 'Элемент 1', status: 'Активный', value: 100 },
              { name: 'Элемент 2', status: 'Неактивный', value: 200 },
              { name: 'Элемент 3', status: 'Активный', value: 300 },
            ]}
            enableRowSelection={true}
            enableExport={true}
            onExport={(data, format) => console.log(`Экспорт ${data.length} записей в ${format}`)}
          />
        </div>

        {/* PropertyTable */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">PropertyTable</h3>
          <PropertyTable
            properties={[
              {
                id: '1',
                name: 'ЖК Солнечный',
                address: 'ул. Ленина, 123',
                type: 'apartment',
                status: 'construction',
                totalUnits: 300,
                availableUnits: 234,
                priceFrom: 2500000,
                priceTo: 8500000,
                completionDate: '2024-12-31',
                developer: 'СтройИнвест',
                createdAt: '2024-01-15',
                updatedAt: '2024-01-27',
              },
              {
                id: '2',
                name: 'Бизнес-центр Альфа',
                address: 'пр. Мира, 45',
                type: 'commercial',
                status: 'ready',
                totalUnits: 50,
                availableUnits: 12,
                priceFrom: 15000000,
                priceTo: 45000000,
                completionDate: '2024-06-30',
                developer: 'КоммерцСтрой',
                createdAt: '2023-08-10',
                updatedAt: '2024-01-20',
              },
            ]}
            onPropertyView={(property) => console.log('Просмотр объекта:', property)}
            onPropertyEdit={(property) => console.log('Редактирование объекта:', property)}
          />
        </div>

        {/* ClientTable */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">ClientTable</h3>
          <ClientTable
            clients={[
              {
                id: '1',
                firstName: 'Иван',
                lastName: 'Петров',
                email: '<EMAIL>',
                phone: '+7 (999) 123-45-67',
                status: 'client',
                source: 'website',
                totalPurchases: 2500000,
                lastActivity: '2024-01-25',
                registrationDate: '2023-12-01',
              },
              {
                id: '2',
                firstName: 'Мария',
                lastName: 'Сидорова',
                email: '<EMAIL>',
                phone: '+7 (999) 987-65-43',
                status: 'prospect',
                source: 'referral',
                totalPurchases: 0,
                lastActivity: '2024-01-26',
                registrationDate: '2024-01-15',
              },
            ]}
            onClientView={(client) => console.log('Просмотр клиента:', client)}
            onClientMessage={(client) => console.log('Сообщение клиенту:', client)}
          />
        </div>
      </section>

      {/* Современная страница авторизации */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Современная страница авторизации</h2>
        <div className="border rounded-lg p-4 bg-gray-50">
          <p className="text-sm text-muted-foreground mb-4">
            Компонент Login03 - современная страница авторизации в стиле shadcn/ui
          </p>
          <div className="max-w-md mx-auto">
            <Login03
              onSubmit={async (data) => {
                console.log('Данные авторизации:', data);
                // Имитация задержки
                await new Promise(resolve => setTimeout(resolve, 1000));
              }}
              title="Демо авторизации"
              subtitle="Пример современной формы входа"
              companyName="PactCRM"
              showForgotPassword={true}
              showSignUp={true}
              onForgotPassword={() => console.log('Забыли пароль')}
              onSignUp={() => console.log('Регистрация')}
            />
          </div>
        </div>
      </section>

      {/* Фаза 3: Графики и визуализация данных */}
      <section className="space-y-6">
        <h2 className="text-xl font-semibold">Фаза 3: Графики и визуализация данных</h2>

        {/* Базовые графики */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium">Базовые графики</h3>

          {/* LineChart */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">LineChart - Линейный график</h4>
            <LineChart
              data={[
                { name: 'Янв', продажи: 4000, выручка: 2400, цель: 3500 },
                { name: 'Фев', продажи: 3000, выручка: 1398, цель: 3500 },
                { name: 'Мар', продажи: 2000, выручка: 9800, цель: 3500 },
                { name: 'Апр', продажи: 2780, выручка: 3908, цель: 3500 },
                { name: 'Май', продажи: 1890, выручка: 4800, цель: 3500 },
                { name: 'Июн', продажи: 2390, выручка: 3800, цель: 3500 },
              ]}
              lines={[
                { dataKey: 'продажи', stroke: '#8884d8', name: 'Продажи' },
                { dataKey: 'выручка', stroke: '#82ca9d', name: 'Выручка' },
                { dataKey: 'цель', stroke: '#ffc658', strokeDasharray: '5 5', name: 'Цель' },
              ]}
              title="Динамика продаж"
              enableExport={true}
            />
          </div>

          {/* BarChart */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">BarChart - Столбчатый график</h4>
            <BarChart
              data={[
                { name: 'Квартиры', продано: 120, доступно: 80, зарезервировано: 30 },
                { name: 'Дома', продано: 45, доступно: 25, зарезервировано: 10 },
                { name: 'Коммерция', продано: 15, доступно: 35, зарезервировано: 5 },
              ]}
              bars={[
                { dataKey: 'продано', fill: '#22c55e', name: 'Продано' },
                { dataKey: 'доступно', fill: '#3b82f6', name: 'Доступно' },
                { dataKey: 'зарезервировано', fill: '#f59e0b', name: 'Зарезервировано' },
              ]}
              title="Статистика по типам объектов"
              enableExport={true}
            />
          </div>

          {/* PieChart */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">PieChart - Круговая диаграмма</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <PieChart
                data={[
                  { name: 'Продано', value: 180, color: '#22c55e' },
                  { name: 'Доступно', value: 140, color: '#3b82f6' },
                  { name: 'Зарезервировано', value: 45, color: '#f59e0b' },
                ]}
                title="Распределение объектов"
                enableExport={true}
                size="sm"
              />

              <DonutChart
                data={[
                  { name: 'В срок', value: 75, color: '#22c55e' },
                  { name: 'Просрочено', value: 15, color: '#ef4444' },
                  { name: 'Ожидает', value: 10, color: '#f59e0b' },
                ]}
                title="Статус платежей"
                enableExport={true}
                size="sm"
              />
            </div>
          </div>
        </div>

        {/* Специализированные дашборд графики */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium">Специализированные дашборд графики</h3>

          {/* SalesChart */}
          <SalesChart
            data={[
              {
                period: 'Q1 2024',
                totalSales: 45000000,
                unitsSold: 18,
                averagePrice: 2500000,
                revenue: 42000000,
                target: 50000000,
              },
              {
                period: 'Q2 2024',
                totalSales: 62000000,
                unitsSold: 25,
                averagePrice: 2480000,
                revenue: 58000000,
                target: 55000000,
              },
              {
                period: 'Q3 2024',
                totalSales: 78000000,
                unitsSold: 31,
                averagePrice: 2516000,
                revenue: 72000000,
                target: 60000000,
              },
            ]}
            chartType="combined"
            showTarget={true}
            showTrend={true}
            showSummary={true}
          />

          {/* PaymentChart */}
          <PaymentChart
            data={[
              {
                period: 'Янв 2024',
                totalPayments: 15000000,
                onTimePayments: 12000000,
                overduePayments: 2000000,
                pendingPayments: 1000000,
                penalties: 150000,
              },
              {
                period: 'Фев 2024',
                totalPayments: 18000000,
                onTimePayments: 15000000,
                overduePayments: 1500000,
                pendingPayments: 1500000,
                penalties: 120000,
              },
              {
                period: 'Мар 2024',
                totalPayments: 22000000,
                onTimePayments: 19000000,
                overduePayments: 1800000,
                pendingPayments: 1200000,
                penalties: 180000,
              },
            ]}
            chartType="combined"
            showBreakdown={true}
            showTrend={true}
            showSummary={true}
          />

          {/* ClientActivityChart */}
          <ClientActivityChart
            data={[
              {
                period: 'Нед 1',
                newClients: 12,
                activeClients: 145,
                inquiries: 28,
                meetings: 15,
                conversions: 8,
                churnedClients: 2,
              },
              {
                period: 'Нед 2',
                newClients: 18,
                activeClients: 158,
                inquiries: 35,
                meetings: 22,
                conversions: 12,
                churnedClients: 1,
              },
              {
                period: 'Нед 3',
                newClients: 15,
                activeClients: 167,
                inquiries: 42,
                meetings: 28,
                conversions: 15,
                churnedClients: 3,
              },
            ]}
            qualityMetrics={[
              { subject: 'Отзывчивость', score: 85 },
              { subject: 'Качество сервиса', score: 92 },
              { subject: 'Скорость ответа', score: 78 },
              { subject: 'Решение проблем', score: 88 },
              { subject: 'Профессионализм', score: 95 },
            ]}
            chartType="combined"
            showConversion={true}
            showTrend={true}
            showSummary={true}
          />

          {/* PropertyStatsChart */}
          <PropertyStatsChart
            data={[
              {
                propertyType: 'Квартиры',
                totalUnits: 200,
                soldUnits: 120,
                availableUnits: 65,
                reservedUnits: 15,
                averagePrice: 2500000,
                totalRevenue: 300000000,
              },
              {
                propertyType: 'Дома',
                totalUnits: 80,
                soldUnits: 45,
                availableUnits: 25,
                reservedUnits: 10,
                averagePrice: 4500000,
                totalRevenue: 202500000,
              },
              {
                propertyType: 'Коммерция',
                totalUnits: 55,
                soldUnits: 15,
                availableUnits: 35,
                reservedUnits: 5,
                averagePrice: 8500000,
                totalRevenue: 127500000,
              },
            ]}
            statusData={[
              { status: 'Планирование', count: 5, color: '#64748b' },
              { status: 'Строительство', count: 12, color: '#f59e0b' },
              { status: 'Готов', count: 8, color: '#22c55e' },
              { status: 'Продан', count: 3, color: '#3b82f6' },
            ]}
            chartType="combined"
            showAvailability={true}
            showRevenue={true}
            showSummary={true}
          />
        </div>
      </section>
    </div>
  );
}
