/**
 * @file: login.tsx
 * @description: Современная страница авторизации SuperAdmin с Login03
 * @dependencies: @pactcrm/ui Login03, @pactcrm/supabase-client
 * @created: 2025-01-26
 * @updated: 2025-01-27
 */

"use client"

import { useState } from 'react'
import { useRouter } from 'next/router'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Login03 } from '@pactcrm/ui'

export default function SuperAdminLoginPage() {
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const supabase = createClientComponentClient()

  const handleLogin = async (data: { email: string; password: string }) => {
    setLoading(true)
    setError(null)

    try {
      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      })

      if (error) {
        throw error
      }

      // Проверяем роль пользователя из всех возможных источников
      const user = authData.user;
      const userRole = user?.user_metadata?.role || user?.raw_user_meta_data?.role || user?.app_metadata?.role;

      console.log('🔍 SuperAdmin Login: Данные пользователя:', {
        id: user?.id,
        email: user?.email,
        user_metadata: user?.user_metadata,
        raw_user_meta_data: user?.raw_user_meta_data,
        app_metadata: user?.app_metadata,
        extractedRole: userRole
      });

      if (userRole !== 'superadmin' && userRole !== 'support') {
        console.log(`❌ SuperAdmin Login: Недостаточно прав. Роль: ${userRole}`);
        // Если пользователь не superadmin или support, выходим из системы
        await supabase.auth.signOut()
        setError('У вас нет прав доступа к панели администратора')
        setLoading(false)
        return
      }

      console.log(`✅ SuperAdmin Login: Роль подтверждена: ${userRole}`);

      // Перенаправляем на дашборд
      router.push('/admin/dashboard')
    } catch (error: any) {
      setError(error.message || 'Произошла ошибка при входе в систему')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Login03
      onSubmit={handleLogin}
      loading={loading}
      error={error}
      title="SuperAdmin панель"
      subtitle="Войдите в панель администратора PactCRM"
      companyName="PactCRM SuperAdmin"
      showForgotPassword={false}
      variant="admin"
    />
  )
}
