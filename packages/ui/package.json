{"name": "@pactcrm/ui", "version": "0.0.0", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "license": "MIT", "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint \"src/**/*.ts*\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "devDependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tabler/icons-react": "^3.33.0", "@tanstack/react-table": "^8.20.5", "@tanstack/match-sorter-utils": "^8.19.4", "react-hook-form": "^7.53.2", "@hookform/resolvers": "^3.10.0", "zod": "^3.24.1", "@types/react": "^18.2.8", "@types/react-dom": "^18.2.4", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^8.56.0", "eslint-config-custom": "workspace:*", "lucide-react": "^0.344.0", "next": "^15.0.0", "next-themes": "^0.2.1", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "tsconfig": "workspace:*", "tsup": "^8.0.2", "typescript": "^5.3.3"}, "peerDependencies": {"next": ">=15.0.0", "react": ">=18.0.0", "react-dom": ">=18.0.0"}, "publishConfig": {"access": "public"}}