/**
 * @file: comparison-card.tsx
 * @description: Карточка сравнения показателей с предыдущим периодом
 * @dependencies: CVA, Radix UI, Tailwind CSS
 * @created: 2025-01-27
 */

"use client";

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { cn } from '../../lib/utils';

// Варианты стилизации для ComparisonCard
const comparisonCardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 overflow-hidden",
  {
    variants: {
      size: {
        sm: "min-h-[140px]",
        md: "min-h-[160px]",
        lg: "min-h-[180px]"
      },
      animated: {
        true: "hover:scale-[1.02] hover:-translate-y-1 hover:shadow-md",
        false: "hover:shadow-md"
      }
    },
    defaultVariants: {
      size: "md",
      animated: false
    }
  }
);

const comparisonBadgeVariants = cva(
  "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
  {
    variants: {
      type: {
        positive: "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400",
        negative: "bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400",
        neutral: "bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400"
      }
    },
    defaultVariants: {
      type: "neutral"
    }
  }
);

export interface ComparisonCardProps extends VariantProps<typeof comparisonCardVariants> {
  title: string;
  current: {
    label: string;
    value: number;
    period?: string;
  };
  previous: {
    label: string;
    value: number;
    period?: string;
  };
  unit?: string;
  comparison: {
    type: 'percentage' | 'absolute';
    isPositive: boolean;
    value: number;
  };
  chart?: {
    data: number[];
    type: 'line' | 'bar';
    color?: string;
  };
  icon?: React.ReactNode;
  loading?: boolean;
  className?: string;
}

export function ComparisonCard({
  title,
  current,
  previous,
  unit = '',
  comparison,
  chart,
  size = "md",
  animated = false,
  icon,
  loading = false,
  className,
}: ComparisonCardProps) {
  if (loading) {
    return (
      <Card className={cn(comparisonCardVariants({ size, animated }), className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="h-4 w-24 bg-muted animate-pulse rounded" />
          {icon && <div className="h-4 w-4 bg-muted animate-pulse rounded" />}
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="h-8 w-32 bg-muted animate-pulse rounded" />
          <div className="h-6 w-28 bg-muted animate-pulse rounded" />
          <div className="h-4 w-20 bg-muted animate-pulse rounded" />
          {chart && <div className="h-16 w-full bg-muted animate-pulse rounded" />}
        </CardContent>
      </Card>
    );
  }

  const comparisonType = comparison.isPositive ? 'positive' : 'negative';
  const comparisonIcon = comparison.isPositive ? '↗' : '↘';
  const comparisonText = comparison.type === 'percentage' 
    ? `${Math.abs(comparison.value)}%`
    : `${Math.abs(comparison.value).toLocaleString()}${unit}`;

  return (
    <Card className={cn(comparisonCardVariants({ size, animated }), className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        {icon && <div className="h-4 w-4 text-muted-foreground">{icon}</div>}
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Текущее значение */}
        <div className="space-y-1">
          <div className="text-2xl font-bold">
            {current.value.toLocaleString()}{unit}
          </div>
          <div className="text-xs text-muted-foreground">
            {current.label} {current.period && `(${current.period})`}
          </div>
        </div>

        {/* Сравнение */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">
              {previous.label} {previous.period && `(${previous.period})`}
            </span>
            <span className="text-sm font-medium">
              {previous.value.toLocaleString()}{unit}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Изменение</span>
            <div className={cn(comparisonBadgeVariants({ type: comparisonType }))}>
              <span>{comparisonIcon}</span>
              <span>{comparisonText}</span>
            </div>
          </div>
        </div>

        {/* Мини-график */}
        {chart && chart.data.length > 0 && (
          <div className="space-y-2">
            <div className="text-xs text-muted-foreground">Тренд</div>
            <div className="h-16 w-full">
              {chart.type === 'line' ? (
                <div className="flex items-end justify-between h-full gap-1">
                  {chart.data.map((value, index) => {
                    const maxValue = Math.max(...chart.data);
                    const height = (value / maxValue) * 100;
                    return (
                      <div
                        key={index}
                        className="flex-1 flex items-end"
                      >
                        <div
                          className={cn(
                            "w-full rounded-t transition-all duration-300",
                            chart.color || "bg-blue-500"
                          )}
                          style={{ height: `${height}%` }}
                        />
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-end justify-between h-full gap-1">
                  {chart.data.map((value, index) => {
                    const maxValue = Math.max(...chart.data);
                    const height = (value / maxValue) * 100;
                    return (
                      <div
                        key={index}
                        className={cn(
                          "flex-1 rounded-t transition-all duration-300",
                          chart.color || "bg-blue-500"
                        )}
                        style={{ height: `${height}%` }}
                      />
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
