/**
 * @file: horizontal-stat-card.tsx
 * @description: Горизонтальная статистическая карточка с иконкой слева/справа
 * @dependencies: CVA, Radix UI, Tailwind CSS
 * @created: 2025-01-27
 */

"use client";

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Card, CardContent } from '../ui/card';
import { cn } from '../../lib/utils';

// Варианты стилизации для HorizontalStatCard
const horizontalStatCardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 overflow-hidden",
  {
    variants: {
      size: {
        sm: "min-h-[80px]",
        md: "min-h-[100px]", 
        lg: "min-h-[120px]"
      },
      color: {
        default: "",
        primary: "border-l-4 border-l-blue-500 hover:border-l-blue-600",
        secondary: "border-l-4 border-l-gray-500 hover:border-l-gray-600",
        success: "border-l-4 border-l-green-500 hover:border-l-green-600",
        warning: "border-l-4 border-l-yellow-500 hover:border-l-yellow-600",
        error: "border-l-4 border-l-red-500 hover:border-l-red-600",
        info: "border-l-4 border-l-blue-400 hover:border-l-blue-500"
      },
      animated: {
        true: "hover:scale-[1.02] hover:-translate-y-1 hover:shadow-md",
        false: "hover:shadow-md"
      }
    },
    defaultVariants: {
      size: "md",
      color: "default",
      animated: false
    }
  }
);

const iconContainerVariants = cva(
  "flex items-center justify-center rounded-lg transition-colors duration-200",
  {
    variants: {
      size: {
        sm: "h-10 w-10",
        md: "h-12 w-12",
        lg: "h-14 w-14"
      },
      color: {
        default: "bg-muted text-muted-foreground",
        primary: "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400",
        secondary: "bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400",
        success: "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400",
        warning: "bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400",
        error: "bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400",
        info: "bg-blue-50 text-blue-500 dark:bg-blue-900/10 dark:text-blue-300"
      },
      background: {
        true: "",
        false: "bg-transparent"
      }
    },
    defaultVariants: {
      size: "md",
      color: "default",
      background: true
    }
  }
);

export interface HorizontalStatCardProps extends VariantProps<typeof horizontalStatCardVariants> {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period?: string;
  };
  iconPosition?: 'left' | 'right';
  iconBackground?: boolean;
  loading?: boolean;
  className?: string;
}

export function HorizontalStatCard({
  title,
  value,
  icon,
  description,
  trend,
  iconPosition = 'left',
  iconBackground = true,
  size = "md",
  color = "default",
  animated = false,
  loading = false,
  className,
}: HorizontalStatCardProps) {
  if (loading) {
    return (
      <Card className={cn(horizontalStatCardVariants({ size, color, animated }), className)}>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className={cn(
              iconContainerVariants({ size, color: "default", background: iconBackground }),
              "bg-muted animate-pulse"
            )} />
            <div className="flex-1 space-y-2">
              <div className="h-4 w-24 bg-muted animate-pulse rounded" />
              <div className="h-6 w-32 bg-muted animate-pulse rounded" />
              <div className="h-3 w-20 bg-muted animate-pulse rounded" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const iconSize = size === 'sm' ? 16 : size === 'md' ? 20 : 24;

  const contentSection = (
    <div className="flex-1 space-y-1">
      <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
      <div className="text-2xl font-bold">{value}</div>
      {(description || trend) && (
        <div className="flex items-center text-xs text-muted-foreground">
          {trend && (
            <span
              className={cn(
                'mr-2 flex items-center gap-1 font-medium',
                trend.isPositive ? 'text-green-500' : 'text-red-500'
              )}
            >
              <span className="text-xs">
                {trend.isPositive ? '↗' : '↘'}
              </span>
              {Math.abs(trend.value)}%
              {trend.period && <span className="text-xs opacity-75">/{trend.period}</span>}
            </span>
          )}
          {description && <span>{description}</span>}
        </div>
      )}
    </div>
  );

  const iconSection = icon && (
    <div className={cn(iconContainerVariants({ size, color, background: iconBackground }))}>
      {React.isValidElement(icon) 
        ? React.cloneElement(icon as React.ReactElement, { 
            size: iconSize,
            className: cn((icon as React.ReactElement).props?.className)
          })
        : icon
      }
    </div>
  );

  return (
    <Card className={cn(horizontalStatCardVariants({ size, color, animated }), className)}>
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          {iconPosition === 'left' && iconSection}
          {contentSection}
          {iconPosition === 'right' && iconSection}
        </div>
      </CardContent>
    </Card>
  );
}
