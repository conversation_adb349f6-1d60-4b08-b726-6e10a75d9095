/**
 * @file: contract-table.tsx
 * @description: Специализированная таблица для договоров
 * @dependencies: AdvancedDataTable, TanStack Table, CVA
 * @created: 2025-01-27
 */

"use client";

import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { AdvancedDataTable, AdvancedDataTableProps } from './advanced-data-table';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  FileText, 
  Calendar, 
  User, 
  Building,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  Eye,
  Edit,
  Download,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

// Типы данных для договоров
export interface Contract {
  id: string;
  number: string;
  clientName: string;
  propertyName: string;
  propertyAddress: string;
  totalAmount: number;
  paidAmount: number;
  status: 'draft' | 'active' | 'completed' | 'cancelled' | 'suspended';
  signedDate?: string;
  completionDate?: string;
  nextPaymentDate?: string;
  nextPaymentAmount?: number;
  createdAt: string;
  updatedAt: string;
}

// Статусы договоров
const contractStatusConfig = {
  draft: { 
    label: 'Черновик', 
    variant: 'secondary' as const, 
    icon: FileText 
  },
  active: { 
    label: 'Активный', 
    variant: 'success' as const, 
    icon: CheckCircle 
  },
  completed: { 
    label: 'Завершен', 
    variant: 'default' as const, 
    icon: CheckCircle 
  },
  cancelled: { 
    label: 'Отменен', 
    variant: 'destructive' as const, 
    icon: AlertCircle 
  },
  suspended: { 
    label: 'Приостановлен', 
    variant: 'warning' as const, 
    icon: Clock 
  },
};

export interface ContractTableProps extends Omit<AdvancedDataTableProps<Contract, any>, 'columns' | 'data'> {
  contracts: Contract[];
  onContractView?: (contract: Contract) => void;
  onContractEdit?: (contract: Contract) => void;
  onContractDownload?: (contract: Contract) => void;
  onContractDelete?: (contract: Contract) => void;
  showActions?: boolean;
}

export function ContractTable({
  contracts,
  onContractView,
  onContractEdit,
  onContractDownload,
  onContractDelete,
  showActions = true,
  ...props
}: ContractTableProps) {
  
  const columns = useMemo<ColumnDef<Contract>[]>(() => {
    const baseColumns: ColumnDef<Contract>[] = [
      {
        accessorKey: 'number',
        header: 'Номер договора',
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            <FileText className="h-3 w-3 text-muted-foreground" />
            <span className="font-mono font-medium">{row.getValue('number')}</span>
          </div>
        ),
      },
      {
        accessorKey: 'clientName',
        header: 'Клиент',
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            <User className="h-3 w-3 text-muted-foreground" />
            <span className="font-medium">{row.getValue('clientName')}</span>
          </div>
        ),
      },
      {
        accessorKey: 'property',
        header: 'Объект',
        cell: ({ row }) => {
          const contract = row.original;
          return (
            <div className="space-y-1">
              <div className="flex items-center space-x-1">
                <Building className="h-3 w-3 text-muted-foreground" />
                <span className="font-medium text-sm">{contract.propertyName}</span>
              </div>
              <div className="text-xs text-muted-foreground">
                {contract.propertyAddress}
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'status',
        header: 'Статус',
        cell: ({ row }) => {
          const status = row.getValue('status') as Contract['status'];
          const config = contractStatusConfig[status];
          const StatusIcon = config.icon;
          
          return (
            <Badge variant={config.variant} className="flex items-center space-x-1">
              <StatusIcon className="h-3 w-3" />
              <span>{config.label}</span>
            </Badge>
          );
        },
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
      },
      {
        accessorKey: 'amount',
        header: 'Сумма договора',
        cell: ({ row }) => {
          const contract = row.original;
          const progress = (contract.paidAmount / contract.totalAmount) * 100;
          
          const formatPrice = (price: number) => 
            new Intl.NumberFormat('ru-RU', { 
              style: 'currency', 
              currency: 'RUB',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            }).format(price);
          
          return (
            <div className="space-y-2">
              <div className="flex items-center space-x-1">
                <DollarSign className="h-3 w-3 text-muted-foreground" />
                <span className="font-medium">{formatPrice(contract.totalAmount)}</span>
              </div>
              <div className="space-y-1">
                <div className="text-xs text-muted-foreground">
                  Оплачено: {formatPrice(contract.paidAmount)} ({progress.toFixed(1)}%)
                </div>
                <div className="w-full bg-muted rounded-full h-1.5">
                  <div 
                    className="bg-primary h-1.5 rounded-full transition-all duration-300" 
                    style={{ width: `${Math.min(progress, 100)}%` }}
                  />
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'nextPayment',
        header: 'Следующий платеж',
        cell: ({ row }) => {
          const contract = row.original;
          if (!contract.nextPaymentDate || !contract.nextPaymentAmount) {
            return <span className="text-muted-foreground">—</span>;
          }
          
          const date = new Date(contract.nextPaymentDate);
          const now = new Date();
          const isOverdue = date < now;
          
          const formatPrice = (price: number) => 
            new Intl.NumberFormat('ru-RU', { 
              style: 'currency', 
              currency: 'RUB',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            }).format(price);
          
          return (
            <div className="space-y-1">
              <div className="flex items-center space-x-1">
                <DollarSign className="h-3 w-3 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {formatPrice(contract.nextPaymentAmount)}
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar className="h-3 w-3 text-muted-foreground" />
                <span className={`text-xs ${isOverdue ? 'text-red-600 font-medium' : ''}`}>
                  {date.toLocaleDateString('ru-RU')}
                </span>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'signedDate',
        header: 'Дата подписания',
        cell: ({ row }) => {
          const signedDate = row.getValue('signedDate') as string;
          if (!signedDate) return <span className="text-muted-foreground">—</span>;
          
          const date = new Date(signedDate);
          return (
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-sm">
                {date.toLocaleDateString('ru-RU')}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: 'completionDate',
        header: 'Дата завершения',
        cell: ({ row }) => {
          const completionDate = row.getValue('completionDate') as string;
          if (!completionDate) return <span className="text-muted-foreground">—</span>;
          
          const date = new Date(completionDate);
          return (
            <div className="flex items-center space-x-1">
              <CheckCircle className="h-3 w-3 text-green-600" />
              <span className="text-sm">
                {date.toLocaleDateString('ru-RU')}
              </span>
            </div>
          );
        },
      },
    ];

    // Добавляем колонку действий если нужно
    if (showActions) {
      baseColumns.push({
        id: 'actions',
        header: 'Действия',
        cell: ({ row }) => {
          const contract = row.original;
          
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Открыть меню</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Действия</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {onContractView && (
                  <DropdownMenuItem onClick={() => onContractView(contract)}>
                    <Eye className="mr-2 h-4 w-4" />
                    Просмотр
                  </DropdownMenuItem>
                )}
                {onContractEdit && (
                  <DropdownMenuItem onClick={() => onContractEdit(contract)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Редактировать
                  </DropdownMenuItem>
                )}
                {onContractDownload && (
                  <DropdownMenuItem onClick={() => onContractDownload(contract)}>
                    <Download className="mr-2 h-4 w-4" />
                    Скачать PDF
                  </DropdownMenuItem>
                )}
                {onContractDelete && (
                  <DropdownMenuItem 
                    onClick={() => onContractDelete(contract)}
                    className="text-destructive"
                  >
                    Удалить
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
        enableSorting: false,
        enableHiding: false,
      });
    }

    return baseColumns;
  }, [showActions, onContractView, onContractEdit, onContractDownload, onContractDelete]);

  // Функция экспорта данных
  const handleExport = (data: Contract[], format: 'csv' | 'excel' | 'pdf') => {
    // Здесь будет логика экспорта
    console.log(`Экспорт ${data.length} договоров в формате ${format}`);
  };

  return (
    <AdvancedDataTable
      columns={columns}
      data={contracts}
      enableRowSelection={true}
      enableExport={true}
      onExport={handleExport}
      emptyMessage="Договоры не найдены"
      {...props}
    />
  );
}
