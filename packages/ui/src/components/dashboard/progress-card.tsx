/**
 * @file: progress-card.tsx
 * @description: Карточка с прогресс-баром и сегментированным отображением
 * @dependencies: CVA, Radix UI, Tailwind CSS
 * @created: 2025-01-27
 */

"use client";

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { cn } from '../../lib/utils';

// Варианты стилизации для ProgressCard
const progressCardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 overflow-hidden",
  {
    variants: {
      size: {
        sm: "min-h-[120px]",
        md: "min-h-[140px]",
        lg: "min-h-[160px]"
      },
      animated: {
        true: "hover:scale-[1.02] hover:-translate-y-1 hover:shadow-md",
        false: "hover:shadow-md"
      }
    },
    defaultVariants: {
      size: "md",
      animated: false
    }
  }
);

const progressBarVariants = cva(
  "h-3 rounded-full transition-all duration-700 ease-out",
  {
    variants: {
      gradient: {
        true: "bg-gradient-to-r",
        false: ""
      },
      animated: {
        true: "animate-pulse",
        false: ""
      }
    },
    defaultVariants: {
      gradient: false,
      animated: false
    }
  }
);

export interface ProgressCardProps extends VariantProps<typeof progressCardVariants> {
  title: string;
  value: number;
  maxValue: number;
  unit?: string;
  description?: string;
  progress: {
    percentage: number;
    color?: string;
    gradient?: boolean;
    animated?: boolean;
  };
  segments?: Array<{
    label: string;
    value: number;
    color: string;
  }>;
  icon?: React.ReactNode;
  loading?: boolean;
  className?: string;
}

export function ProgressCard({
  title,
  value,
  maxValue,
  unit = '',
  description,
  progress,
  segments,
  size = "md",
  animated = false,
  icon,
  loading = false,
  className,
}: ProgressCardProps) {
  if (loading) {
    return (
      <Card className={cn(progressCardVariants({ size, animated }), className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="h-4 w-24 bg-muted animate-pulse rounded" />
          {icon && <div className="h-4 w-4 bg-muted animate-pulse rounded" />}
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="h-8 w-32 bg-muted animate-pulse rounded" />
          <div className="h-3 w-20 bg-muted animate-pulse rounded" />
          <div className="space-y-2">
            <div className="h-3 w-full bg-muted animate-pulse rounded" />
            <div className="h-3 w-16 bg-muted animate-pulse rounded" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const progressColor = progress.color || 'bg-blue-500';
  const progressPercentage = Math.min(progress.percentage, 100);

  return (
    <Card className={cn(progressCardVariants({ size, animated }), className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        {icon && <div className="h-4 w-4 text-muted-foreground">{icon}</div>}
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Значение и максимум */}
        <div className="space-y-1">
          <div className="text-2xl font-bold">
            {value.toLocaleString()}{unit}
            <span className="text-sm font-normal text-muted-foreground ml-2">
              / {maxValue.toLocaleString()}{unit}
            </span>
          </div>
          {description && (
            <div className="text-xs text-muted-foreground">{description}</div>
          )}
        </div>

        {/* Основной прогресс бар */}
        <div className="space-y-2">
          <div className="w-full bg-muted rounded-full h-3">
            <div 
              className={cn(
                progressBarVariants({ 
                  gradient: progress.gradient, 
                  animated: progress.animated 
                }),
                progress.gradient 
                  ? `from-${progressColor.replace('bg-', '')}-400 to-${progressColor.replace('bg-', '')}-600`
                  : progressColor
              )}
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          <div className="flex justify-between items-center text-xs">
            <span className="font-medium text-foreground">
              {progressPercentage.toFixed(1)}%
            </span>
            <span className="text-muted-foreground">
              {(maxValue - value).toLocaleString()}{unit} осталось
            </span>
          </div>
        </div>

        {/* Сегментированный прогресс */}
        {segments && segments.length > 0 && (
          <div className="space-y-3">
            <div className="text-xs font-medium text-muted-foreground">Детализация:</div>
            <div className="space-y-2">
              {segments.map((segment, index) => {
                const segmentPercentage = (segment.value / maxValue) * 100;
                return (
                  <div key={index} className="space-y-1">
                    <div className="flex justify-between items-center text-xs">
                      <span className="font-medium">{segment.label}</span>
                      <span className="text-muted-foreground">
                        {segment.value.toLocaleString()}{unit} ({segmentPercentage.toFixed(1)}%)
                      </span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-1.5">
                      <div 
                        className={cn("h-1.5 rounded-full transition-all duration-500", segment.color)}
                        style={{ width: `${Math.min(segmentPercentage, 100)}%` }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
