/**
 * @file: index.ts
 * @description: Экспорты всех dashboard компонентов
 * @created: 2025-01-27
 */

// Базовые компоненты
export { StatCard } from './stat-card';
export type { StatCardProps } from './stat-card';

export { ChartCard } from './chart-card';
export { DataTable } from './data-table';
export { TaskList } from './task-list';

// Новые статистические компоненты (Фаза 1)
export { HorizontalStatCard } from './horizontal-stat-card';
export type { HorizontalStatCardProps } from './horizontal-stat-card';

export { StatCardWithBorder } from './stat-card-with-border';
export type { StatCardWithBorderProps } from './stat-card-with-border';

export { KPICard } from './kpi-card';
export type { KPICardProps } from './kpi-card';

export { ProgressCard } from './progress-card';
export type { ProgressCardProps } from './progress-card';

export { ComparisonCard } from './comparison-card';
export type { ComparisonCardProps } from './comparison-card';

// Продвинутые таблицы (Фаза 2)
export { AdvancedDataTable } from './advanced-data-table';
export type { AdvancedDataTableProps } from './advanced-data-table';

// Специализированные таблицы
export { PropertyTable } from './property-table';
export type { PropertyTableProps, Property } from './property-table';

export { ClientTable } from './client-table';
export type { ClientTableProps, Client } from './client-table';

export { PaymentTable } from './payment-table';
export type { PaymentTableProps, Payment } from './payment-table';

export { ContractTable } from './contract-table';
export type { ContractTableProps, Contract } from './contract-table';
