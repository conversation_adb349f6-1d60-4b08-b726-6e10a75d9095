/**
 * @file: payment-table.tsx
 * @description: Специализированная таблица для платежей
 * @dependencies: AdvancedDataTable, TanStack Table, CVA
 * @created: 2025-01-27
 */

"use client";

import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { AdvancedDataTable, AdvancedDataTableProps } from './advanced-data-table';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  DollarSign, 
  Calendar, 
  User, 
  CreditCard,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Edit,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

// Типы данных для платежей
export interface Payment {
  id: string;
  contractId: string;
  clientName: string;
  amount: number;
  dueDate: string;
  paidDate?: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  paymentMethod?: 'cash' | 'card' | 'bank_transfer' | 'online';
  description: string;
  penalty?: number;
  createdAt: string;
  updatedAt: string;
}

// Статусы платежей
const paymentStatusConfig = {
  pending: { 
    label: 'Ожидает', 
    variant: 'warning' as const, 
    icon: Clock 
  },
  paid: { 
    label: 'Оплачен', 
    variant: 'success' as const, 
    icon: CheckCircle 
  },
  overdue: { 
    label: 'Просрочен', 
    variant: 'destructive' as const, 
    icon: AlertCircle 
  },
  cancelled: { 
    label: 'Отменен', 
    variant: 'secondary' as const, 
    icon: AlertCircle 
  },
};

// Методы оплаты
const paymentMethodConfig = {
  cash: { label: 'Наличные' },
  card: { label: 'Карта' },
  bank_transfer: { label: 'Банковский перевод' },
  online: { label: 'Онлайн' },
};

export interface PaymentTableProps extends Omit<AdvancedDataTableProps<Payment, any>, 'columns' | 'data'> {
  payments: Payment[];
  onPaymentView?: (payment: Payment) => void;
  onPaymentEdit?: (payment: Payment) => void;
  onPaymentMarkPaid?: (payment: Payment) => void;
  onPaymentDelete?: (payment: Payment) => void;
  showActions?: boolean;
}

export function PaymentTable({
  payments,
  onPaymentView,
  onPaymentEdit,
  onPaymentMarkPaid,
  onPaymentDelete,
  showActions = true,
  ...props
}: PaymentTableProps) {
  
  const columns = useMemo<ColumnDef<Payment>[]>(() => {
    const baseColumns: ColumnDef<Payment>[] = [
      {
        accessorKey: 'id',
        header: 'ID платежа',
        cell: ({ row }) => (
          <div className="font-mono text-sm">
            #{row.getValue('id').slice(-6)}
          </div>
        ),
      },
      {
        accessorKey: 'clientName',
        header: 'Клиент',
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            <User className="h-3 w-3 text-muted-foreground" />
            <span className="font-medium">{row.getValue('clientName')}</span>
          </div>
        ),
      },
      {
        accessorKey: 'amount',
        header: 'Сумма',
        cell: ({ row }) => {
          const amount = row.getValue('amount') as number;
          const penalty = row.original.penalty || 0;
          const total = amount + penalty;
          
          const formatPrice = (price: number) => 
            new Intl.NumberFormat('ru-RU', { 
              style: 'currency', 
              currency: 'RUB',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            }).format(price);
          
          return (
            <div className="space-y-1">
              <div className="flex items-center space-x-1">
                <DollarSign className="h-3 w-3 text-muted-foreground" />
                <span className="font-medium">{formatPrice(amount)}</span>
              </div>
              {penalty > 0 && (
                <div className="text-xs text-red-600">
                  + {formatPrice(penalty)} пеня
                </div>
              )}
              {penalty > 0 && (
                <div className="text-xs font-medium">
                  Итого: {formatPrice(total)}
                </div>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'status',
        header: 'Статус',
        cell: ({ row }) => {
          const status = row.getValue('status') as Payment['status'];
          const config = paymentStatusConfig[status];
          const StatusIcon = config.icon;
          
          return (
            <Badge variant={config.variant} className="flex items-center space-x-1">
              <StatusIcon className="h-3 w-3" />
              <span>{config.label}</span>
            </Badge>
          );
        },
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
      },
      {
        accessorKey: 'dueDate',
        header: 'Срок оплаты',
        cell: ({ row }) => {
          const dueDate = new Date(row.getValue('dueDate'));
          const now = new Date();
          const isOverdue = dueDate < now && row.original.status === 'pending';
          
          return (
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className={`text-sm ${isOverdue ? 'text-red-600 font-medium' : ''}`}>
                {dueDate.toLocaleDateString('ru-RU')}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: 'paidDate',
        header: 'Дата оплаты',
        cell: ({ row }) => {
          const paidDate = row.getValue('paidDate') as string;
          if (!paidDate) return <span className="text-muted-foreground">—</span>;
          
          const date = new Date(paidDate);
          return (
            <div className="flex items-center space-x-1">
              <CheckCircle className="h-3 w-3 text-green-600" />
              <span className="text-sm">
                {date.toLocaleDateString('ru-RU')}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: 'paymentMethod',
        header: 'Способ оплаты',
        cell: ({ row }) => {
          const method = row.getValue('paymentMethod') as Payment['paymentMethod'];
          if (!method) return <span className="text-muted-foreground">—</span>;
          
          return (
            <div className="flex items-center space-x-1">
              <CreditCard className="h-3 w-3 text-muted-foreground" />
              <span className="text-sm">
                {paymentMethodConfig[method].label}
              </span>
            </div>
          );
        },
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
      },
      {
        accessorKey: 'description',
        header: 'Описание',
        cell: ({ row }) => (
          <div className="text-sm max-w-[200px] truncate">
            {row.getValue('description')}
          </div>
        ),
      },
    ];

    // Добавляем колонку действий если нужно
    if (showActions) {
      baseColumns.push({
        id: 'actions',
        header: 'Действия',
        cell: ({ row }) => {
          const payment = row.original;
          
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Открыть меню</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Действия</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {onPaymentView && (
                  <DropdownMenuItem onClick={() => onPaymentView(payment)}>
                    <Eye className="mr-2 h-4 w-4" />
                    Просмотр
                  </DropdownMenuItem>
                )}
                {onPaymentEdit && (
                  <DropdownMenuItem onClick={() => onPaymentEdit(payment)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Редактировать
                  </DropdownMenuItem>
                )}
                {onPaymentMarkPaid && payment.status === 'pending' && (
                  <DropdownMenuItem onClick={() => onPaymentMarkPaid(payment)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Отметить как оплаченный
                  </DropdownMenuItem>
                )}
                {onPaymentDelete && (
                  <DropdownMenuItem 
                    onClick={() => onPaymentDelete(payment)}
                    className="text-destructive"
                  >
                    Удалить
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
        enableSorting: false,
        enableHiding: false,
      });
    }

    return baseColumns;
  }, [showActions, onPaymentView, onPaymentEdit, onPaymentMarkPaid, onPaymentDelete]);

  // Функция экспорта данных
  const handleExport = (data: Payment[], format: 'csv' | 'excel' | 'pdf') => {
    // Здесь будет логика экспорта
    console.log(`Экспорт ${data.length} платежей в формате ${format}`);
  };

  return (
    <AdvancedDataTable
      columns={columns}
      data={payments}
      enableRowSelection={true}
      enableExport={true}
      onExport={handleExport}
      emptyMessage="Платежи не найдены"
      {...props}
    />
  );
}
