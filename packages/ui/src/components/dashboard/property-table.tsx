/**
 * @file: property-table.tsx
 * @description: Специализированная таблица для объектов недвижимости
 * @dependencies: AdvancedDataTable, TanStack Table, CVA
 * @created: 2025-01-27
 */

"use client";

import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { AdvancedDataTable, AdvancedDataTableProps } from './advanced-data-table';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  Building, 
  MapPin, 
  Calendar, 
  Users, 
  DollarSign,
  Eye,
  Edit,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

// Типы данных для объектов недвижимости
export interface Property {
  id: string;
  name: string;
  address: string;
  type: 'apartment' | 'house' | 'commercial' | 'land';
  status: 'planning' | 'construction' | 'ready' | 'sold';
  totalUnits: number;
  availableUnits: number;
  priceFrom: number;
  priceTo: number;
  completionDate: string;
  developer: string;
  createdAt: string;
  updatedAt: string;
}

// Статусы объектов
const propertyStatusConfig = {
  planning: { label: 'Планирование', variant: 'secondary' as const },
  construction: { label: 'Строительство', variant: 'warning' as const },
  ready: { label: 'Готов', variant: 'success' as const },
  sold: { label: 'Продан', variant: 'default' as const },
};

// Типы объектов
const propertyTypeConfig = {
  apartment: { label: 'Квартиры', icon: Building },
  house: { label: 'Дома', icon: Building },
  commercial: { label: 'Коммерция', icon: Building },
  land: { label: 'Земля', icon: MapPin },
};

export interface PropertyTableProps extends Omit<AdvancedDataTableProps<Property, any>, 'columns' | 'data'> {
  properties: Property[];
  onPropertyView?: (property: Property) => void;
  onPropertyEdit?: (property: Property) => void;
  onPropertyDelete?: (property: Property) => void;
  showActions?: boolean;
}

export function PropertyTable({
  properties,
  onPropertyView,
  onPropertyEdit,
  onPropertyDelete,
  showActions = true,
  ...props
}: PropertyTableProps) {
  
  const columns = useMemo<ColumnDef<Property>[]>(() => {
    const baseColumns: ColumnDef<Property>[] = [
      {
        accessorKey: 'name',
        header: 'Название',
        cell: ({ row }) => {
          const property = row.original;
          const TypeIcon = propertyTypeConfig[property.type].icon;
          return (
            <div className="flex items-center space-x-2">
              <TypeIcon className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="font-medium">{property.name}</div>
                <div className="text-sm text-muted-foreground">
                  {propertyTypeConfig[property.type].label}
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'address',
        header: 'Адрес',
        cell: ({ row }) => (
          <div className="flex items-center space-x-1">
            <MapPin className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{row.getValue('address')}</span>
          </div>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Статус',
        cell: ({ row }) => {
          const status = row.getValue('status') as Property['status'];
          const config = propertyStatusConfig[status];
          return (
            <Badge variant={config.variant}>
              {config.label}
            </Badge>
          );
        },
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
      },
      {
        accessorKey: 'totalUnits',
        header: 'Всего единиц',
        cell: ({ row }) => (
          <div className="flex items-center space-x-1">
            <Users className="h-3 w-3 text-muted-foreground" />
            <span>{row.getValue('totalUnits')}</span>
          </div>
        ),
      },
      {
        accessorKey: 'availableUnits',
        header: 'Доступно',
        cell: ({ row }) => {
          const available = row.getValue('availableUnits') as number;
          const total = row.original.totalUnits;
          const percentage = Math.round((available / total) * 100);
          
          return (
            <div className="space-y-1">
              <div className="text-sm font-medium">{available} из {total}</div>
              <div className="w-full bg-muted rounded-full h-1.5">
                <div 
                  className="bg-primary h-1.5 rounded-full transition-all duration-300" 
                  style={{ width: `${percentage}%` }}
                />
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'priceRange',
        header: 'Цена',
        cell: ({ row }) => {
          const property = row.original;
          const formatPrice = (price: number) => 
            new Intl.NumberFormat('ru-RU', { 
              style: 'currency', 
              currency: 'RUB',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            }).format(price);
          
          return (
            <div className="flex items-center space-x-1">
              <DollarSign className="h-3 w-3 text-muted-foreground" />
              <div className="text-sm">
                {formatPrice(property.priceFrom)} - {formatPrice(property.priceTo)}
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'completionDate',
        header: 'Дата сдачи',
        cell: ({ row }) => {
          const date = new Date(row.getValue('completionDate'));
          return (
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-sm">
                {date.toLocaleDateString('ru-RU')}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: 'developer',
        header: 'Застройщик',
        cell: ({ row }) => (
          <div className="text-sm">{row.getValue('developer')}</div>
        ),
      },
    ];

    // Добавляем колонку действий если нужно
    if (showActions) {
      baseColumns.push({
        id: 'actions',
        header: 'Действия',
        cell: ({ row }) => {
          const property = row.original;
          
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Открыть меню</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Действия</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {onPropertyView && (
                  <DropdownMenuItem onClick={() => onPropertyView(property)}>
                    <Eye className="mr-2 h-4 w-4" />
                    Просмотр
                  </DropdownMenuItem>
                )}
                {onPropertyEdit && (
                  <DropdownMenuItem onClick={() => onPropertyEdit(property)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Редактировать
                  </DropdownMenuItem>
                )}
                {onPropertyDelete && (
                  <DropdownMenuItem 
                    onClick={() => onPropertyDelete(property)}
                    className="text-destructive"
                  >
                    Удалить
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
        enableSorting: false,
        enableHiding: false,
      });
    }

    return baseColumns;
  }, [showActions, onPropertyView, onPropertyEdit, onPropertyDelete]);

  // Функция экспорта данных
  const handleExport = (data: Property[], format: 'csv' | 'excel' | 'pdf') => {
    // Здесь будет логика экспорта
    console.log(`Экспорт ${data.length} объектов в формате ${format}`);
  };

  return (
    <AdvancedDataTable
      columns={columns}
      data={properties}
      enableRowSelection={true}
      enableExport={true}
      onExport={handleExport}
      emptyMessage="Объекты недвижимости не найдены"
      {...props}
    />
  );
}
