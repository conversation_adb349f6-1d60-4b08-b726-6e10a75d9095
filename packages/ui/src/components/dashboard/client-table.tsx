/**
 * @file: client-table.tsx
 * @description: Специализированная таблица для клиентов
 * @dependencies: AdvancedDataTable, TanStack Table, CVA
 * @created: 2025-01-27
 */

"use client";

import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { AdvancedDataTable, AdvancedDataTableProps } from './advanced-data-table';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { 
  User, 
  Phone, 
  Mail, 
  Calendar, 
  DollarSign,
  Eye,
  Edit,
  MessageCircle,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

// Типы данных для клиентов
export interface Client {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  avatar?: string;
  status: 'lead' | 'prospect' | 'client' | 'inactive';
  source: 'website' | 'referral' | 'advertising' | 'social' | 'other';
  totalPurchases: number;
  lastActivity: string;
  registrationDate: string;
  notes?: string;
}

// Статусы клиентов
const clientStatusConfig = {
  lead: { label: 'Лид', variant: 'secondary' as const },
  prospect: { label: 'Потенциальный', variant: 'warning' as const },
  client: { label: 'Клиент', variant: 'success' as const },
  inactive: { label: 'Неактивный', variant: 'outline' as const },
};

// Источники клиентов
const clientSourceConfig = {
  website: { label: 'Сайт' },
  referral: { label: 'Рекомендация' },
  advertising: { label: 'Реклама' },
  social: { label: 'Соц. сети' },
  other: { label: 'Другое' },
};

export interface ClientTableProps extends Omit<AdvancedDataTableProps<Client, any>, 'columns' | 'data'> {
  clients: Client[];
  onClientView?: (client: Client) => void;
  onClientEdit?: (client: Client) => void;
  onClientMessage?: (client: Client) => void;
  onClientDelete?: (client: Client) => void;
  showActions?: boolean;
}

export function ClientTable({
  clients,
  onClientView,
  onClientEdit,
  onClientMessage,
  onClientDelete,
  showActions = true,
  ...props
}: ClientTableProps) {
  
  const columns = useMemo<ColumnDef<Client>[]>(() => {
    const baseColumns: ColumnDef<Client>[] = [
      {
        accessorKey: 'fullName',
        header: 'Клиент',
        cell: ({ row }) => {
          const client = row.original;
          const fullName = `${client.firstName} ${client.lastName}`;
          const initials = `${client.firstName[0]}${client.lastName[0]}`;
          
          return (
            <div className="flex items-center space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={client.avatar} alt={fullName} />
                <AvatarFallback>{initials}</AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">{fullName}</div>
                <div className="text-sm text-muted-foreground">
                  {clientSourceConfig[client.source].label}
                </div>
              </div>
            </div>
          );
        },
        filterFn: (row, id, value) => {
          const client = row.original;
          const fullName = `${client.firstName} ${client.lastName}`.toLowerCase();
          return fullName.includes(value.toLowerCase());
        },
      },
      {
        accessorKey: 'email',
        header: 'Email',
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            <Mail className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{row.getValue('email')}</span>
          </div>
        ),
      },
      {
        accessorKey: 'phone',
        header: 'Телефон',
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            <Phone className="h-3 w-3 text-muted-foreground" />
            <span className="text-sm">{row.getValue('phone')}</span>
          </div>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Статус',
        cell: ({ row }) => {
          const status = row.getValue('status') as Client['status'];
          const config = clientStatusConfig[status];
          return (
            <Badge variant={config.variant}>
              {config.label}
            </Badge>
          );
        },
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
      },
      {
        accessorKey: 'totalPurchases',
        header: 'Покупки',
        cell: ({ row }) => {
          const amount = row.getValue('totalPurchases') as number;
          const formatPrice = (price: number) => 
            new Intl.NumberFormat('ru-RU', { 
              style: 'currency', 
              currency: 'RUB',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            }).format(price);
          
          return (
            <div className="flex items-center space-x-1">
              <DollarSign className="h-3 w-3 text-muted-foreground" />
              <span className="text-sm font-medium">
                {amount > 0 ? formatPrice(amount) : '—'}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: 'lastActivity',
        header: 'Последняя активность',
        cell: ({ row }) => {
          const date = new Date(row.getValue('lastActivity'));
          const now = new Date();
          const diffTime = Math.abs(now.getTime() - date.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
          let timeAgo = '';
          if (diffDays === 1) {
            timeAgo = 'вчера';
          } else if (diffDays < 7) {
            timeAgo = `${diffDays} дн. назад`;
          } else if (diffDays < 30) {
            timeAgo = `${Math.floor(diffDays / 7)} нед. назад`;
          } else {
            timeAgo = date.toLocaleDateString('ru-RU');
          }
          
          return (
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-sm">{timeAgo}</span>
            </div>
          );
        },
      },
      {
        accessorKey: 'registrationDate',
        header: 'Дата регистрации',
        cell: ({ row }) => {
          const date = new Date(row.getValue('registrationDate'));
          return (
            <div className="text-sm">
              {date.toLocaleDateString('ru-RU')}
            </div>
          );
        },
      },
    ];

    // Добавляем колонку действий если нужно
    if (showActions) {
      baseColumns.push({
        id: 'actions',
        header: 'Действия',
        cell: ({ row }) => {
          const client = row.original;
          
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Открыть меню</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Действия</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {onClientView && (
                  <DropdownMenuItem onClick={() => onClientView(client)}>
                    <Eye className="mr-2 h-4 w-4" />
                    Просмотр
                  </DropdownMenuItem>
                )}
                {onClientEdit && (
                  <DropdownMenuItem onClick={() => onClientEdit(client)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Редактировать
                  </DropdownMenuItem>
                )}
                {onClientMessage && (
                  <DropdownMenuItem onClick={() => onClientMessage(client)}>
                    <MessageCircle className="mr-2 h-4 w-4" />
                    Написать сообщение
                  </DropdownMenuItem>
                )}
                {onClientDelete && (
                  <DropdownMenuItem 
                    onClick={() => onClientDelete(client)}
                    className="text-destructive"
                  >
                    Удалить
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
        enableSorting: false,
        enableHiding: false,
      });
    }

    return baseColumns;
  }, [showActions, onClientView, onClientEdit, onClientMessage, onClientDelete]);

  // Функция экспорта данных
  const handleExport = (data: Client[], format: 'csv' | 'excel' | 'pdf') => {
    // Здесь будет логика экспорта
    console.log(`Экспорт ${data.length} клиентов в формате ${format}`);
  };

  return (
    <AdvancedDataTable
      columns={columns}
      data={clients}
      enableRowSelection={true}
      enableExport={true}
      onExport={handleExport}
      emptyMessage="Клиенты не найдены"
      {...props}
    />
  );
}
