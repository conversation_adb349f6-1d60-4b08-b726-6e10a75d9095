/**
 * @file: kpi-card.tsx
 * @description: KPI карточка с прогрессом к цели и статусом выполнения
 * @dependencies: CVA, Radix UI, Tailwind CSS
 * @created: 2025-01-27
 */

"use client";

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { cn } from '../../lib/utils';

// Варианты стилизации для KPICard
const kpiCardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 overflow-hidden",
  {
    variants: {
      status: {
        excellent: "border-l-4 border-l-green-500 bg-green-50/50 dark:bg-green-950/20",
        good: "border-l-4 border-l-blue-500 bg-blue-50/50 dark:bg-blue-950/20", 
        warning: "border-l-4 border-l-yellow-500 bg-yellow-50/50 dark:bg-yellow-950/20",
        critical: "border-l-4 border-l-red-500 bg-red-50/50 dark:bg-red-950/20"
      },
      size: {
        sm: "min-h-[120px]",
        md: "min-h-[140px]",
        lg: "min-h-[160px]"
      },
      animated: {
        true: "hover:scale-[1.02] hover:-translate-y-1 hover:shadow-md",
        false: "hover:shadow-md"
      }
    },
    defaultVariants: {
      status: "good",
      size: "md", 
      animated: false
    }
  }
);

const progressBarVariants = cva(
  "h-2 rounded-full transition-all duration-500 ease-out",
  {
    variants: {
      status: {
        excellent: "bg-green-500",
        good: "bg-blue-500",
        warning: "bg-yellow-500", 
        critical: "bg-red-500"
      }
    },
    defaultVariants: {
      status: "good"
    }
  }
);

export interface KPICardProps extends VariantProps<typeof kpiCardVariants> {
  title: string;
  current: number;
  target: number;
  unit?: string;
  period?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period?: string;
  };
  progress?: {
    value: number;
    color?: string;
    showLabel?: boolean;
  };
  icon?: React.ReactNode;
  loading?: boolean;
  className?: string;
}

export function KPICard({
  title,
  current,
  target,
  unit = '',
  period = 'месяц',
  trend,
  progress,
  status = "good",
  size = "md",
  animated = false,
  icon,
  loading = false,
  className,
}: KPICardProps) {
  // Вычисляем прогресс если не передан явно
  const progressValue = progress?.value ?? Math.min((current / target) * 100, 100);
  const progressLabel = progress?.showLabel ?? true;
  
  // Определяем статус на основе прогресса если не задан явно
  const autoStatus = progressValue >= 90 ? 'excellent' : 
                    progressValue >= 70 ? 'good' : 
                    progressValue >= 50 ? 'warning' : 'critical';
  
  const finalStatus = status || autoStatus;

  if (loading) {
    return (
      <Card className={cn(kpiCardVariants({ status: "good", size, animated }), className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="h-4 w-24 bg-muted animate-pulse rounded" />
          {icon && <div className="h-4 w-4 bg-muted animate-pulse rounded" />}
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="h-8 w-32 bg-muted animate-pulse rounded" />
          <div className="h-3 w-20 bg-muted animate-pulse rounded" />
          <div className="space-y-2">
            <div className="h-2 w-full bg-muted animate-pulse rounded" />
            <div className="h-3 w-16 bg-muted animate-pulse rounded" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const statusColors = {
    excellent: { text: 'text-green-600', bg: 'text-green-500' },
    good: { text: 'text-blue-600', bg: 'text-blue-500' },
    warning: { text: 'text-yellow-600', bg: 'text-yellow-500' },
    critical: { text: 'text-red-600', bg: 'text-red-500' }
  };

  return (
    <Card className={cn(kpiCardVariants({ status: finalStatus, size, animated }), className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        {icon && (
          <div className={cn("h-4 w-4", statusColors[finalStatus].text)}>
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Текущее значение и цель */}
        <div className="space-y-1">
          <div className="text-2xl font-bold">
            {current.toLocaleString()}{unit}
            <span className="text-sm font-normal text-muted-foreground ml-2">
              / {target.toLocaleString()}{unit}
            </span>
          </div>
          <div className="text-xs text-muted-foreground">
            Цель на {period}
          </div>
        </div>

        {/* Прогресс бар */}
        <div className="space-y-2">
          <div className="w-full bg-muted rounded-full h-2">
            <div 
              className={cn(progressBarVariants({ status: finalStatus }))}
              style={{ width: `${Math.min(progressValue, 100)}%` }}
            />
          </div>
          {progressLabel && (
            <div className="flex justify-between items-center text-xs">
              <span className={cn("font-medium", statusColors[finalStatus].text)}>
                {progressValue.toFixed(1)}% выполнено
              </span>
              <span className="text-muted-foreground">
                {(target - current).toLocaleString()}{unit} до цели
              </span>
            </div>
          )}
        </div>

        {/* Тренд */}
        {trend && (
          <div className="flex items-center text-xs text-muted-foreground">
            <span
              className={cn(
                'flex items-center gap-1 font-medium',
                trend.isPositive ? 'text-green-500' : 'text-red-500'
              )}
            >
              <span className="text-xs">
                {trend.isPositive ? '↗' : '↘'}
              </span>
              {Math.abs(trend.value)}%
              {trend.period && <span className="opacity-75">/{trend.period}</span>}
            </span>
            <span className="ml-2">относительно прошлого периода</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
