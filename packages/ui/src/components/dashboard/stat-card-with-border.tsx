/**
 * @file: stat-card-with-border.tsx
 * @description: Статистическая карточка с цветной границей и hover эффектами (вдохновлено Vuexy)
 * @dependencies: CVA, Radix UI, Tailwind CSS
 * @created: 2025-01-27
 */

"use client";

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { cn } from '../../lib/utils';

// Варианты стилизации для StatCardWithBorder
const statCardWithBorderVariants = cva(
  "rounded-lg bg-card text-card-foreground shadow-sm transition-all duration-300 overflow-hidden",
  {
    variants: {
      borderPosition: {
        left: "border-l-4",
        top: "border-t-4", 
        right: "border-r-4",
        bottom: "border-b-4"
      },
      borderWidth: {
        thin: "",
        medium: "",
        thick: ""
      },
      color: {
        default: "border-gray-300",
        primary: "border-blue-500",
        secondary: "border-gray-500", 
        success: "border-green-500",
        warning: "border-yellow-500",
        error: "border-red-500",
        info: "border-blue-400"
      },
      size: {
        sm: "min-h-[100px]",
        md: "min-h-[120px]",
        lg: "min-h-[140px]"
      },
      hoverEffect: {
        true: "hover:shadow-lg cursor-pointer",
        false: ""
      }
    },
    compoundVariants: [
      // Left border hover effects
      {
        borderPosition: "left",
        borderWidth: "thin",
        hoverEffect: true,
        class: "hover:border-l-[3px] hover:-ml-px"
      },
      {
        borderPosition: "left", 
        borderWidth: "medium",
        hoverEffect: true,
        class: "hover:border-l-[6px] hover:-ml-0.5"
      },
      {
        borderPosition: "left",
        borderWidth: "thick", 
        hoverEffect: true,
        class: "hover:border-l-[8px] hover:-ml-1"
      },
      // Top border hover effects
      {
        borderPosition: "top",
        borderWidth: "medium",
        hoverEffect: true,
        class: "hover:border-t-[6px] hover:-mt-0.5"
      },
      // Color-specific hover effects
      {
        color: "primary",
        hoverEffect: true,
        class: "hover:border-blue-600"
      },
      {
        color: "success", 
        hoverEffect: true,
        class: "hover:border-green-600"
      },
      {
        color: "warning",
        hoverEffect: true, 
        class: "hover:border-yellow-600"
      },
      {
        color: "error",
        hoverEffect: true,
        class: "hover:border-red-600"
      },
      {
        color: "info",
        hoverEffect: true,
        class: "hover:border-blue-500"
      }
    ],
    defaultVariants: {
      borderPosition: "left",
      borderWidth: "medium", 
      color: "primary",
      size: "md",
      hoverEffect: true
    }
  }
);

export interface StatCardWithBorderProps extends VariantProps<typeof statCardWithBorderVariants> {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  description?: string;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period?: string;
  };
  loading?: boolean;
  onClick?: () => void;
  className?: string;
}

export function StatCardWithBorder({
  title,
  value,
  icon,
  description,
  subtitle,
  trend,
  borderPosition = "left",
  borderWidth = "medium",
  color = "primary", 
  size = "md",
  hoverEffect = true,
  loading = false,
  onClick,
  className,
}: StatCardWithBorderProps) {
  if (loading) {
    return (
      <Card className={cn(
        statCardWithBorderVariants({ borderPosition, borderWidth, color, size, hoverEffect }),
        "border-gray-200",
        className
      )}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="h-4 w-24 bg-muted animate-pulse rounded" />
          {icon && <div className="h-4 w-4 bg-muted animate-pulse rounded" />}
        </CardHeader>
        <CardContent>
          <div className="h-8 w-32 bg-muted animate-pulse rounded mb-2" />
          <div className="h-3 w-20 bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      className={cn(
        statCardWithBorderVariants({ borderPosition, borderWidth, color, size, hoverEffect }),
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        {icon && (
          <div className={cn(
            "h-4 w-4",
            color === "primary" && "text-blue-500",
            color === "success" && "text-green-500", 
            color === "warning" && "text-yellow-500",
            color === "error" && "text-red-500",
            color === "info" && "text-blue-400",
            color === "secondary" && "text-gray-500",
            color === "default" && "text-muted-foreground"
          )}>
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {subtitle && (
          <div className="text-sm font-medium mt-1 text-muted-foreground">{subtitle}</div>
        )}
        {(description || trend) && (
          <div className="flex items-center text-xs text-muted-foreground mt-2">
            {trend && (
              <span
                className={cn(
                  'mr-2 flex items-center gap-1 font-medium',
                  trend.isPositive ? 'text-green-500' : 'text-red-500'
                )}
              >
                <span className="text-xs">
                  {trend.isPositive ? '↗' : '↘'}
                </span>
                {Math.abs(trend.value)}%
                {trend.period && <span className="text-xs opacity-75">/{trend.period}</span>}
              </span>
            )}
            {description && <span>{description}</span>}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
