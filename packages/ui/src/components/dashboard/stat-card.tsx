/**
 * @file: stat-card.tsx
 * @description: Улучшенный статистический компонент с поддержкой вариантов и расширенной функциональности
 * @dependencies: CVA, Radix UI, Tailwind CSS
 * @created: 2025-01-27
 */

"use client";

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { cn } from '../../lib/utils';

// Варианты стилизации для StatCard
const statCardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 overflow-hidden",
  {
    variants: {
      variant: {
        default: "p-0",
        horizontal: "p-0",
        bordered: "border-l-4 hover:shadow-md hover:border-l-[6px] transition-all duration-300",
        gradient: "bg-gradient-to-br text-white border-0 shadow-lg"
      },
      size: {
        sm: "min-h-[100px]",
        md: "min-h-[120px]",
        lg: "min-h-[140px]"
      },
      color: {
        default: "",
        primary: "",
        secondary: "",
        success: "border-l-green-500 hover:border-l-green-600",
        warning: "border-l-yellow-500 hover:border-l-yellow-600",
        error: "border-l-red-500 hover:border-l-red-600",
        info: "border-l-blue-500 hover:border-l-blue-600"
      },
      animated: {
        true: "hover:scale-[1.02] hover:-translate-y-1",
        false: ""
      }
    },
    compoundVariants: [
      {
        variant: "gradient",
        color: "primary",
        class: "from-blue-500 to-blue-600"
      },
      {
        variant: "gradient",
        color: "success",
        class: "from-green-500 to-green-600"
      },
      {
        variant: "gradient",
        color: "warning",
        class: "from-yellow-500 to-yellow-600"
      },
      {
        variant: "gradient",
        color: "error",
        class: "from-red-500 to-red-600"
      },
      {
        variant: "gradient",
        color: "info",
        class: "from-blue-400 to-blue-500"
      }
    ],
    defaultVariants: {
      variant: "default",
      size: "md",
      color: "default",
      animated: false
    }
  }
);

export interface StatCardProps extends VariantProps<typeof statCardVariants> {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  description?: string;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period?: string;
  };
  loading?: boolean;
  className?: string;
}

export function StatCard({
  title,
  value,
  icon,
  description,
  subtitle,
  trend,
  variant = "default",
  size = "md",
  color = "default",
  animated = false,
  loading = false,
  className,
}: StatCardProps) {
  if (loading) {
    return (
      <Card className={cn(statCardVariants({ variant, size, color, animated }), className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="h-4 w-24 bg-muted animate-pulse rounded" />
          {icon && <div className="h-4 w-4 bg-muted animate-pulse rounded" />}
        </CardHeader>
        <CardContent>
          <div className="h-8 w-32 bg-muted animate-pulse rounded mb-2" />
          <div className="h-3 w-20 bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    );
  }

  const isGradient = variant === "gradient";
  const iconColor = isGradient ? "text-white/80" : "text-muted-foreground";
  const titleColor = isGradient ? "text-white/90" : "";
  const valueColor = isGradient ? "text-white" : "";
  const descriptionColor = isGradient ? "text-white/70" : "text-muted-foreground";

  return (
    <Card className={cn(statCardVariants({ variant, size, color, animated }), className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className={cn("text-sm font-medium", titleColor)}>{title}</CardTitle>
        {icon && <div className={cn("h-4 w-4", iconColor)}>{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className={cn("text-2xl font-bold", valueColor)}>{value}</div>
        {subtitle && (
          <div className={cn("text-sm font-medium mt-1", titleColor)}>{subtitle}</div>
        )}
        {(description || trend) && (
          <div className={cn("flex items-center text-xs mt-2", descriptionColor)}>
            {trend && (
              <span
                className={cn(
                  'mr-2 flex items-center gap-1 font-medium',
                  isGradient
                    ? (trend.isPositive ? 'text-green-200' : 'text-red-200')
                    : (trend.isPositive ? 'text-green-500' : 'text-red-500')
                )}
              >
                <span className="text-xs">
                  {trend.isPositive ? '↗' : '↘'}
                </span>
                {Math.abs(trend.value)}%
                {trend.period && <span className="text-xs opacity-75">/{trend.period}</span>}
              </span>
            )}
            {description && <span>{description}</span>}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
