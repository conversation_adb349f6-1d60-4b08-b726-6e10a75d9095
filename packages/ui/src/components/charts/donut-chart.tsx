/**
 * @file: donut-chart.tsx
 * @description: Компонент кольцевой диаграммы на основе Recharts
 * @dependencies: Recharts, CVA, TypeScript
 * @created: 2025-01-27
 */

"use client";

import React, { useRef } from 'react';
import {
  PieChart as Recharts<PERSON>ie<PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Download } from 'lucide-react';
import html2canvas from 'html2canvas';

// Варианты стилей для графика
const donutChartVariants = cva(
  "w-full",
  {
    variants: {
      size: {
        sm: "h-64",
        md: "h-80",
        lg: "h-96",
        xl: "h-[32rem]",
      },
      variant: {
        default: "",
        minimal: "[&_.recharts-legend]:hidden",
        compact: "h-48",
        thick: "", // Толстое кольцо
        thin: "",  // Тонкое кольцо
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  }
);

// Типы данных
export interface DonutDataPoint {
  name: string;
  value: number;
  color?: string;
}

export interface DonutChartProps extends VariantProps<typeof donutChartVariants> {
  data: DonutDataPoint[];
  title?: string;
  showLegend?: boolean;
  showTooltip?: boolean;
  enableExport?: boolean;
  colors?: string[];
  innerRadius?: number;
  outerRadius?: number;
  showLabels?: boolean;
  showValues?: boolean;
  showPercentages?: boolean;
  centerContent?: React.ReactNode;
  className?: string;
  onSliceClick?: (data: DonutDataPoint, index: number) => void;
}

// Цветовая палитра по умолчанию
const defaultColors = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1',
  '#d084d0', '#ffb347', '#87ceeb', '#dda0dd', '#98fb98'
];

export function DonutChart({
  data,
  title,
  showLegend = true,
  showTooltip = true,
  enableExport = false,
  colors = defaultColors,
  innerRadius,
  outerRadius = 80,
  showLabels = false,
  showValues = false,
  showPercentages = true,
  centerContent,
  size,
  variant,
  className,
  onSliceClick,
}: DonutChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  // Определяем внутренний радиус в зависимости от варианта
  const getInnerRadius = () => {
    if (innerRadius !== undefined) return innerRadius;
    
    switch (variant) {
      case 'thick':
        return outerRadius * 0.3;
      case 'thin':
        return outerRadius * 0.7;
      default:
        return outerRadius * 0.5;
    }
  };

  // Вычисляем общую сумму для процентов
  const totalValue = data.reduce((sum, item) => sum + item.value, 0);

  // Функция экспорта графика в изображение
  const handleExport = async () => {
    if (!chartRef.current) return;

    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: 'white',
        scale: 2,
      });
      
      const link = document.createElement('a');
      link.download = `${title || 'donut-chart'}-${new Date().toISOString().split('T')[0]}.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Ошибка экспорта графика:', error);
    }
  };

  // Кастомный tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      const percentage = ((data.value / totalValue) * 100).toFixed(1);
      
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{data.name}</p>
          <p className="text-sm text-muted-foreground">
            Значение: {data.value.toLocaleString()}
          </p>
          <p className="text-sm text-muted-foreground">
            Процент: {percentage}%
          </p>
        </div>
      );
    }
    return null;
  };

  // Кастомные метки
  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, value, name }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 1.2;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    if (percent < 0.05) return null; // Не показываем метки для слишком маленьких сегментов

    let labelText = '';
    if (showLabels && showPercentages && showValues) {
      labelText = `${name} (${(percent * 100).toFixed(1)}%)`;
    } else if (showLabels && showPercentages) {
      labelText = `${name} (${(percent * 100).toFixed(1)}%)`;
    } else if (showLabels && showValues) {
      labelText = `${name} (${value})`;
    } else if (showPercentages) {
      labelText = `${(percent * 100).toFixed(1)}%`;
    } else if (showValues) {
      labelText = `${value}`;
    } else if (showLabels) {
      labelText = name;
    }

    return (
      <text 
        x={x} 
        y={y} 
        fill="#374151" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize="12"
        fontWeight="500"
      >
        {labelText}
      </text>
    );
  };

  // Контент в центре кольца
  const CenterContent = () => {
    if (!centerContent) {
      return (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl font-bold text-foreground">
              {totalValue.toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">
              Всего
            </div>
          </div>
        </div>
      );
    }
    
    return (
      <div className="absolute inset-0 flex items-center justify-center">
        {centerContent}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Заголовок и кнопка экспорта */}
      {(title || enableExport) && (
        <div className="flex items-center justify-between">
          {title && (
            <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          )}
          {enableExport && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              className="flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Экспорт</span>
            </Button>
          )}
        </div>
      )}

      {/* График */}
      <div 
        ref={chartRef}
        className={cn(donutChartVariants({ size, variant }), "relative", className)}
      >
        <ResponsiveContainer width="100%" height="100%">
          <RechartsPieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={showLabels || showValues || showPercentages ? <CustomLabel /> : false}
              outerRadius={outerRadius}
              innerRadius={getInnerRadius()}
              fill="#8884d8"
              dataKey="value"
              onClick={onSliceClick}
              className="cursor-pointer"
            >
              {data.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={entry.color || colors[index % colors.length]}
                  className="hover:opacity-80 transition-opacity"
                />
              ))}
            </Pie>
            
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            
            {showLegend && (
              <Legend 
                verticalAlign="bottom"
                height={36}
                iconType="circle"
                wrapperStyle={{ paddingTop: '20px' }}
              />
            )}
          </RechartsPieChart>
        </ResponsiveContainer>
        
        {/* Контент в центре */}
        <CenterContent />
      </div>
    </div>
  );
}
