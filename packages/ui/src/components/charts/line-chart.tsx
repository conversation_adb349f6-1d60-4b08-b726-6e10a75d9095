/**
 * @file: line-chart.tsx
 * @description: Базовый компонент линейного графика на основе Recharts
 * @dependencies: Recharts, CVA, TypeScript
 * @created: 2025-01-27
 */

"use client";

import React, { useRef } from 'react';
import {
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Download } from 'lucide-react';
import html2canvas from 'html2canvas';

// Варианты стилей для графика
const lineChartVariants = cva(
  "w-full",
  {
    variants: {
      size: {
        sm: "h-64",
        md: "h-80",
        lg: "h-96",
        xl: "h-[32rem]",
      },
      variant: {
        default: "",
        minimal: "[&_.recharts-cartesian-grid]:opacity-30",
        bold: "[&_.recharts-line]:stroke-width-3",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  }
);

// Типы данных
export interface ChartDataPoint {
  name: string;
  [key: string]: string | number;
}

export interface LineConfig {
  dataKey: string;
  stroke: string;
  strokeWidth?: number;
  strokeDasharray?: string;
  name?: string;
  type?: 'monotone' | 'linear' | 'step' | 'stepBefore' | 'stepAfter';
}

export interface LineChartProps extends VariantProps<typeof lineChartVariants> {
  data: ChartDataPoint[];
  lines: LineConfig[];
  title?: string;
  showGrid?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  enableExport?: boolean;
  xAxisDataKey?: string;
  yAxisLabel?: string;
  xAxisLabel?: string;
  colors?: string[];
  className?: string;
  onDataPointClick?: (data: any, index: number) => void;
}

// Цветовая палитра по умолчанию
const defaultColors = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1',
  '#d084d0', '#ffb347', '#87ceeb', '#dda0dd', '#98fb98'
];

export function LineChart({
  data,
  lines,
  title,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  enableExport = false,
  xAxisDataKey = 'name',
  yAxisLabel,
  xAxisLabel,
  colors = defaultColors,
  size,
  variant,
  className,
  onDataPointClick,
}: LineChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  // Функция экспорта графика в изображение
  const handleExport = async () => {
    if (!chartRef.current) return;

    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: 'white',
        scale: 2,
      });
      
      const link = document.createElement('a');
      link.download = `${title || 'line-chart'}-${new Date().toISOString().split('T')[0]}.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Ошибка экспорта графика:', error);
    }
  };

  // Кастомный tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-4">
      {/* Заголовок и кнопка экспорта */}
      {(title || enableExport) && (
        <div className="flex items-center justify-between">
          {title && (
            <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          )}
          {enableExport && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              className="flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Экспорт</span>
            </Button>
          )}
        </div>
      )}

      {/* График */}
      <div 
        ref={chartRef}
        className={cn(lineChartVariants({ size, variant }), className)}
      >
        <ResponsiveContainer width="100%" height="100%">
          <RechartsLineChart
            data={data}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
            onClick={onDataPointClick}
          >
            {showGrid && (
              <CartesianGrid 
                strokeDasharray="3 3" 
                className="stroke-muted opacity-30"
              />
            )}
            
            <XAxis 
              dataKey={xAxisDataKey}
              className="text-xs fill-muted-foreground"
              label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -5 } : undefined}
            />
            
            <YAxis 
              className="text-xs fill-muted-foreground"
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined}
            />
            
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            
            {showLegend && (
              <Legend 
                wrapperStyle={{ paddingTop: '20px' }}
                iconType="line"
              />
            )}
            
            {lines.map((line, index) => (
              <Line
                key={line.dataKey}
                type={line.type || 'monotone'}
                dataKey={line.dataKey}
                stroke={line.stroke || colors[index % colors.length]}
                strokeWidth={line.strokeWidth || 2}
                strokeDasharray={line.strokeDasharray}
                name={line.name || line.dataKey}
                dot={{ r: 4 }}
                activeDot={{ r: 6, className: 'drop-shadow-sm' }}
                connectNulls={false}
              />
            ))}
          </RechartsLineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
