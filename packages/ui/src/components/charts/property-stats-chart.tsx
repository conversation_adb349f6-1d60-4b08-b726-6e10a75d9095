/**
 * @file: property-stats-chart.tsx
 * @description: Специализированный график статистики объектов недвижимости для PactCRM
 * @dependencies: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Donut<PERSON>hart
 * @created: 2025-01-27
 */

"use client";

import React, { useMemo } from 'react';
import { BarChart } from './bar-chart';
import { PieChart } from './pie-chart';
import { DonutChart } from './donut-chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Building, Home, MapPin, TrendingUp } from 'lucide-react';

// Типы данных для статистики объектов
export interface PropertyStatsDataPoint {
  propertyType: string; // Тип объекта (квартиры, дома, коммерция)
  totalUnits: number; // Общее количество единиц
  soldUnits: number; // Проданные единицы
  availableUnits: number; // Доступные единицы
  reservedUnits: number; // Зарезервированные единицы
  averagePrice: number; // Средняя цена
  totalRevenue: number; // Общая выручка
}

// Данные по статусам объектов
export interface PropertyStatusData {
  status: string; // Статус (планирование, строительство, готов, продан)
  count: number; // Количество объектов
  color?: string;
}

export interface PropertyStatsChartProps {
  data: PropertyStatsDataPoint[];
  statusData?: PropertyStatusData[];
  title?: string;
  chartType?: 'bar' | 'pie' | 'donut' | 'combined';
  showAvailability?: boolean;
  showRevenue?: boolean;
  showSummary?: boolean;
  className?: string;
}

export function PropertyStatsChart({
  data,
  statusData = [],
  title = "Статистика объектов недвижимости",
  chartType = 'combined',
  showAvailability = true,
  showRevenue = true,
  showSummary = true,
  className,
}: PropertyStatsChartProps) {
  
  // Подготавливаем данные для основного графика
  const chartData = useMemo(() => {
    return data.map(item => ({
      name: item.propertyType,
      'Всего единиц': item.totalUnits,
      'Продано': item.soldUnits,
      'Доступно': item.availableUnits,
      'Зарезервировано': item.reservedUnits,
      'Средняя цена (млн ₽)': Math.round(item.averagePrice / 1000000),
      'Выручка (млн ₽)': Math.round(item.totalRevenue / 1000000),
      'Заполненность %': Math.round((item.soldUnits / item.totalUnits) * 100),
    }));
  }, [data]);

  // Данные для круговой диаграммы доступности
  const availabilityData = useMemo(() => {
    const totals = data.reduce(
      (acc, item) => ({
        sold: acc.sold + item.soldUnits,
        available: acc.available + item.availableUnits,
        reserved: acc.reserved + item.reservedUnits,
      }),
      { sold: 0, available: 0, reserved: 0 }
    );

    return [
      { name: 'Продано', value: totals.sold, color: '#22c55e' },
      { name: 'Доступно', value: totals.available, color: '#3b82f6' },
      { name: 'Зарезервировано', value: totals.reserved, color: '#f59e0b' },
    ];
  }, [data]);

  // Общая статистика
  const summary = useMemo(() => {
    const totalUnits = data.reduce((sum, item) => sum + item.totalUnits, 0);
    const soldUnits = data.reduce((sum, item) => sum + item.soldUnits, 0);
    const availableUnits = data.reduce((sum, item) => sum + item.availableUnits, 0);
    const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);
    
    const occupancyRate = totalUnits > 0 ? (soldUnits / totalUnits) * 100 : 0;
    const avgPrice = soldUnits > 0 ? totalRevenue / soldUnits : 0;
    
    return {
      totalUnits,
      soldUnits,
      availableUnits,
      totalRevenue,
      occupancyRate,
      avgPrice,
    };
  }, [data]);

  // Иконки для типов объектов
  const getPropertyIcon = (type: string) => {
    const lowerType = type.toLowerCase();
    if (lowerType.includes('квартир') || lowerType.includes('apartment')) {
      return <Building className="h-4 w-4" />;
    } else if (lowerType.includes('дом') || lowerType.includes('house')) {
      return <Home className="h-4 w-4" />;
    } else {
      return <MapPin className="h-4 w-4" />;
    }
  };

  // Конфигурация для разных типов графиков
  const getChartConfig = () => {
    const colors = {
      total: '#64748b',
      sold: '#22c55e',
      available: '#3b82f6',
      reserved: '#f59e0b',
      revenue: '#8b5cf6',
    };
    
    switch (chartType) {
      case 'bar':
        return {
          component: BarChart,
          props: {
            bars: showRevenue ? [
              { dataKey: 'Продано', fill: colors.sold, name: 'Продано' },
              { dataKey: 'Доступно', fill: colors.available, name: 'Доступно' },
              { dataKey: 'Зарезервировано', fill: colors.reserved, name: 'Зарезервировано' },
              { dataKey: 'Выручка (млн ₽)', fill: colors.revenue, name: 'Выручка' },
            ] : [
              { dataKey: 'Продано', fill: colors.sold, name: 'Продано' },
              { dataKey: 'Доступно', fill: colors.available, name: 'Доступно' },
              { dataKey: 'Зарезервировано', fill: colors.reserved, name: 'Зарезервировано' },
            ],
          },
        };
      
      case 'pie':
        return {
          component: PieChart,
          props: {
            data: showAvailability ? availabilityData : statusData,
          },
        };
      
      case 'donut':
        return {
          component: DonutChart,
          props: {
            data: showAvailability ? availabilityData : statusData,
            centerContent: (
              <div className="text-center">
                <div className="text-lg font-bold">
                  {summary.totalUnits}
                </div>
                <div className="text-xs text-muted-foreground">
                  Всего единиц
                </div>
              </div>
            ),
          },
        };
      
      default: // combined
        return {
          component: BarChart,
          props: {
            bars: [
              { dataKey: 'Продано', fill: colors.sold, name: 'Продано' },
              { dataKey: 'Доступно', fill: colors.available, name: 'Доступно' },
              { dataKey: 'Зарезервировано', fill: colors.reserved, name: 'Зарезервировано' },
            ],
          },
        };
    }
  };

  const chartConfig = getChartConfig();
  const ChartComponent = chartConfig.component;

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Building className="h-5 w-5" />
              <span>{title}</span>
            </CardTitle>
            <CardDescription>
              {chartType === 'pie' || chartType === 'donut' 
                ? (showAvailability ? 'Распределение по доступности' : 'Распределение по статусам')
                : 'Анализ заполненности и продаж по типам объектов'
              }
            </CardDescription>
          </div>
          
          {/* Показатель заполненности */}
          {showSummary && (
            <Badge variant={summary.occupancyRate >= 70 ? 'success' : summary.occupancyRate >= 40 ? 'warning' : 'destructive'} 
                   className="flex items-center space-x-1">
              <TrendingUp className="h-3 w-3" />
              <span>Заполненность {summary.occupancyRate.toFixed(1)}%</span>
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Сводная статистика */}
        {showSummary && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary flex items-center justify-center space-x-1">
                <Building className="h-5 w-5" />
                <span>{summary.totalUnits}</span>
              </div>
              <div className="text-sm text-muted-foreground">Всего единиц</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {summary.soldUnits}
              </div>
              <div className="text-sm text-muted-foreground">Продано</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {summary.availableUnits}
              </div>
              <div className="text-sm text-muted-foreground">Доступно</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {(summary.totalRevenue / 1000000).toFixed(1)}М ₽
              </div>
              <div className="text-sm text-muted-foreground">Выручка</div>
            </div>
          </div>
        )}

        {/* Детализация по типам объектов */}
        {chartType === 'combined' && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {data.map((item, index) => (
              <div key={item.propertyType} className="border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  {getPropertyIcon(item.propertyType)}
                  <span className="font-medium">{item.propertyType}</span>
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Всего:</span>
                    <span className="font-medium">{item.totalUnits}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Продано:</span>
                    <span className="font-medium text-green-600">{item.soldUnits}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Заполненность:</span>
                    <span className="font-medium">
                      {Math.round((item.soldUnits / item.totalUnits) * 100)}%
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {/* График */}
        <div className={chartType === 'pie' || chartType === 'donut' ? 'h-80' : ''}>
          <ChartComponent
            data={chartType === 'pie' || chartType === 'donut' 
              ? (showAvailability ? availabilityData : statusData)
              : chartData
            }
            enableExport={true}
            showGrid={chartType === 'bar'}
            showLegend={true}
            showTooltip={true}
            size={chartType === 'pie' || chartType === 'donut' ? 'md' : 'lg'}
            {...chartConfig.props}
          />
        </div>
      </CardContent>
    </Card>
  );
}
