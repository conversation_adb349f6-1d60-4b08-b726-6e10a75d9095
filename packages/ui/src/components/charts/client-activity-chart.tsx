/**
 * @file: client-activity-chart.tsx
 * @description: Специализированный график активности клиентов для PactCRM
 * @dependencies: LineChart, BarChart, RadarChart
 * @created: 2025-01-27
 */

"use client";

import React, { useMemo } from 'react';
import { LineChart } from './line-chart';
import { BarChart } from './bar-chart';
import { RadarChart } from './radar-chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Users, UserPlus, MessageCircle, TrendingUp } from 'lucide-react';

// Типы данных для активности клиентов
export interface ClientActivityDataPoint {
  period: string; // Месяц или неделя
  newClients: number; // Новые клиенты
  activeClients: number; // Активные клиенты
  inquiries: number; // Обращения
  meetings: number; // Встречи
  conversions: number; // Конверсии
  churnedClients: number; // Ушедшие клиенты
}

// Данные для радарной диаграммы (качественные показатели)
export interface ClientQualityMetrics {
  subject: string;
  score: number; // Оценка от 0 до 100
}

export interface ClientActivityChartProps {
  data: ClientActivityDataPoint[];
  qualityMetrics?: ClientQualityMetrics[];
  title?: string;
  chartType?: 'line' | 'bar' | 'radar' | 'combined';
  showConversion?: boolean;
  showTrend?: boolean;
  showSummary?: boolean;
  className?: string;
}

export function ClientActivityChart({
  data,
  qualityMetrics = [],
  title = "Активность клиентов",
  chartType = 'combined',
  showConversion = true,
  showTrend = true,
  showSummary = true,
  className,
}: ClientActivityChartProps) {
  
  // Подготавливаем данные для основного графика
  const chartData = useMemo(() => {
    return data.map(item => ({
      name: item.period,
      'Новые клиенты': item.newClients,
      'Активные клиенты': item.activeClients,
      'Обращения': item.inquiries,
      'Встречи': item.meetings,
      'Конверсии': item.conversions,
      'Ушедшие клиенты': item.churnedClients,
      'Конверсия %': item.inquiries > 0 ? Math.round((item.conversions / item.inquiries) * 100) : 0,
    }));
  }, [data]);

  // Вычисляем тренды
  const trends = useMemo(() => {
    if (data.length < 2) return null;
    
    const current = data[data.length - 1];
    const previous = data[data.length - 2];
    
    const newClientsTrend = ((current.newClients - previous.newClients) / (previous.newClients || 1)) * 100;
    const activeClientsTrend = ((current.activeClients - previous.activeClients) / previous.activeClients) * 100;
    const conversionTrend = current.inquiries > 0 && previous.inquiries > 0 
      ? ((current.conversions / current.inquiries) - (previous.conversions / previous.inquiries)) * 100
      : 0;
    
    return {
      newClients: newClientsTrend,
      activeClients: activeClientsTrend,
      conversion: conversionTrend,
    };
  }, [data]);

  // Общая статистика
  const summary = useMemo(() => {
    const totalNewClients = data.reduce((sum, item) => sum + item.newClients, 0);
    const totalInquiries = data.reduce((sum, item) => sum + item.inquiries, 0);
    const totalConversions = data.reduce((sum, item) => sum + item.conversions, 0);
    const totalChurned = data.reduce((sum, item) => sum + item.churnedClients, 0);
    
    const avgConversionRate = totalInquiries > 0 ? (totalConversions / totalInquiries) * 100 : 0;
    const churnRate = totalNewClients > 0 ? (totalChurned / totalNewClients) * 100 : 0;
    const currentActive = data.length > 0 ? data[data.length - 1].activeClients : 0;
    
    return {
      totalNewClients,
      totalInquiries,
      totalConversions,
      avgConversionRate,
      churnRate,
      currentActive,
    };
  }, [data]);

  // Конфигурация для разных типов графиков
  const getChartConfig = () => {
    const colors = {
      newClients: '#3b82f6',
      activeClients: '#22c55e',
      inquiries: '#f59e0b',
      meetings: '#8b5cf6',
      conversions: '#10b981',
      churned: '#ef4444',
    };
    
    switch (chartType) {
      case 'line':
        return {
          component: LineChart,
          props: {
            lines: [
              { dataKey: 'Новые клиенты', stroke: colors.newClients, name: 'Новые' },
              { dataKey: 'Активные клиенты', stroke: colors.activeClients, name: 'Активные' },
              { dataKey: 'Конверсии', stroke: colors.conversions, name: 'Конверсии' },
            ],
          },
        };
      
      case 'bar':
        return {
          component: BarChart,
          props: {
            bars: [
              { dataKey: 'Новые клиенты', fill: colors.newClients, name: 'Новые' },
              { dataKey: 'Обращения', fill: colors.inquiries, name: 'Обращения' },
              { dataKey: 'Встречи', fill: colors.meetings, name: 'Встречи' },
              { dataKey: 'Конверсии', fill: colors.conversions, name: 'Конверсии' },
            ],
          },
        };
      
      case 'radar':
        return {
          component: RadarChart,
          props: {
            data: qualityMetrics,
            radars: [
              { dataKey: 'score', stroke: colors.activeClients, fill: colors.activeClients, name: 'Оценка' },
            ],
          },
        };
      
      default: // combined
        return {
          component: LineChart,
          props: {
            lines: [
              { dataKey: 'Новые клиенты', stroke: colors.newClients, name: 'Новые клиенты' },
              { dataKey: 'Активные клиенты', stroke: colors.activeClients, name: 'Активные клиенты' },
              { dataKey: 'Обращения', stroke: colors.inquiries, name: 'Обращения' },
              { dataKey: 'Конверсии', stroke: colors.conversions, name: 'Конверсии' },
              ...(showConversion ? [{ dataKey: 'Конверсия %', stroke: colors.meetings, strokeDasharray: '5 5', name: 'Конверсия %' }] : []),
            ],
          },
        };
    }
  };

  const chartConfig = getChartConfig();
  const ChartComponent = chartConfig.component;

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>{title}</span>
            </CardTitle>
            <CardDescription>
              {chartType === 'radar' ? 'Качественные показатели работы с клиентами' : 'Динамика активности и конверсии клиентов'}
            </CardDescription>
          </div>
          
          {/* Тренды */}
          {showTrend && trends && chartType !== 'radar' && (
            <div className="flex space-x-2">
              <Badge variant={trends.newClients >= 0 ? 'success' : 'destructive'} className="flex items-center space-x-1">
                <UserPlus className="h-3 w-3" />
                <span>{trends.newClients > 0 ? '+' : ''}{trends.newClients.toFixed(1)}%</span>
              </Badge>
              {showConversion && (
                <Badge variant={trends.conversion >= 0 ? 'success' : 'destructive'} className="flex items-center space-x-1">
                  <TrendingUp className="h-3 w-3" />
                  <span>Конверсия {trends.conversion > 0 ? '+' : ''}{trends.conversion.toFixed(1)}%</span>
                </Badge>
              )}
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Сводная статистика */}
        {showSummary && summary && chartType !== 'radar' && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 flex items-center justify-center space-x-1">
                <Users className="h-5 w-5" />
                <span>{summary.currentActive}</span>
              </div>
              <div className="text-sm text-muted-foreground">Активных клиентов</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 flex items-center justify-center space-x-1">
                <UserPlus className="h-5 w-5" />
                <span>{summary.totalNewClients}</span>
              </div>
              <div className="text-sm text-muted-foreground">Новых клиентов</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 flex items-center justify-center space-x-1">
                <TrendingUp className="h-5 w-5" />
                <span>{summary.avgConversionRate.toFixed(1)}%</span>
              </div>
              <div className="text-sm text-muted-foreground">Конверсия</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 flex items-center justify-center space-x-1">
                <MessageCircle className="h-5 w-5" />
                <span>{summary.totalInquiries}</span>
              </div>
              <div className="text-sm text-muted-foreground">Обращений</div>
            </div>
          </div>
        )}
        
        {/* График */}
        <ChartComponent
          data={chartType === 'radar' ? qualityMetrics : chartData}
          enableExport={true}
          showGrid={chartType !== 'radar'}
          showLegend={true}
          showTooltip={true}
          size="lg"
          {...chartConfig.props}
        />
      </CardContent>
    </Card>
  );
}
