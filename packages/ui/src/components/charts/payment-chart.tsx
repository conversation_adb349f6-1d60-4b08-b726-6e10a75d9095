/**
 * @file: payment-chart.tsx
 * @description: Специализированный график платежей и задолженностей для PactCRM
 * @dependencies: AreaChart, BarChart, DonutChart
 * @created: 2025-01-27
 */

"use client";

import React, { useMemo } from 'react';
import { AreaChart } from './area-chart';
import { BarChart } from './bar-chart';
import { DonutChart } from './donut-chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { CreditCard, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

// Типы данных для платежей
export interface PaymentDataPoint {
  period: string; // Месяц или квартал
  totalPayments: number; // Общая сумма платежей
  onTimePayments: number; // Платежи в срок
  overduePayments: number; // Просроченные платежи
  pendingPayments: number; // Ожидающие платежи
  penalties: number; // Штрафы и пени
}

export interface PaymentChartProps {
  data: PaymentDataPoint[];
  title?: string;
  chartType?: 'area' | 'bar' | 'donut' | 'combined';
  showBreakdown?: boolean;
  showTrend?: boolean;
  showSummary?: boolean;
  className?: string;
}

export function PaymentChart({
  data,
  title = "Платежи и задолженности",
  chartType = 'combined',
  showBreakdown = true,
  showTrend = true,
  showSummary = true,
  className,
}: PaymentChartProps) {
  
  // Подготавливаем данные для основного графика
  const chartData = useMemo(() => {
    return data.map(item => ({
      name: item.period,
      'Платежи в срок (млн ₽)': Math.round(item.onTimePayments / 1000000),
      'Просроченные (млн ₽)': Math.round(item.overduePayments / 1000000),
      'Ожидающие (млн ₽)': Math.round(item.pendingPayments / 1000000),
      'Штрафы (тыс ₽)': Math.round(item.penalties / 1000),
      'Общие платежи (млн ₽)': Math.round(item.totalPayments / 1000000),
    }));
  }, [data]);

  // Данные для круговой диаграммы (последний период)
  const donutData = useMemo(() => {
    if (data.length === 0) return [];
    
    const latest = data[data.length - 1];
    return [
      { name: 'Платежи в срок', value: latest.onTimePayments, color: '#22c55e' },
      { name: 'Просроченные', value: latest.overduePayments, color: '#ef4444' },
      { name: 'Ожидающие', value: latest.pendingPayments, color: '#f59e0b' },
    ];
  }, [data]);

  // Вычисляем тренды
  const trends = useMemo(() => {
    if (data.length < 2) return null;
    
    const current = data[data.length - 1];
    const previous = data[data.length - 2];
    
    const totalTrend = ((current.totalPayments - previous.totalPayments) / previous.totalPayments) * 100;
    const overdueTrend = ((current.overduePayments - previous.overduePayments) / (previous.overduePayments || 1)) * 100;
    const onTimeTrend = ((current.onTimePayments - previous.onTimePayments) / previous.onTimePayments) * 100;
    
    return {
      total: totalTrend,
      overdue: overdueTrend,
      onTime: onTimeTrend,
    };
  }, [data]);

  // Общая статистика
  const summary = useMemo(() => {
    const latest = data[data.length - 1];
    if (!latest) return null;
    
    const totalPayments = latest.totalPayments;
    const onTimeRate = (latest.onTimePayments / totalPayments) * 100;
    const overdueRate = (latest.overduePayments / totalPayments) * 100;
    const totalPenalties = data.reduce((sum, item) => sum + item.penalties, 0);
    
    return {
      totalPayments,
      onTimeRate,
      overdueRate,
      totalPenalties,
      latest,
    };
  }, [data]);

  // Конфигурация для разных типов графиков
  const getChartConfig = () => {
    const colors = {
      onTime: '#22c55e',
      overdue: '#ef4444',
      pending: '#f59e0b',
      penalties: '#8b5cf6',
      total: '#3b82f6',
    };
    
    switch (chartType) {
      case 'area':
        return {
          component: AreaChart,
          props: {
            areas: [
              { dataKey: 'Платежи в срок (млн ₽)', fill: colors.onTime, stroke: colors.onTime, name: 'В срок' },
              { dataKey: 'Просроченные (млн ₽)', fill: colors.overdue, stroke: colors.overdue, name: 'Просроченные' },
              { dataKey: 'Ожидающие (млн ₽)', fill: colors.pending, stroke: colors.pending, name: 'Ожидающие' },
            ],
            stacked: true,
          },
        };
      
      case 'bar':
        return {
          component: BarChart,
          props: {
            bars: [
              { dataKey: 'Платежи в срок (млн ₽)', fill: colors.onTime, name: 'В срок' },
              { dataKey: 'Просроченные (млн ₽)', fill: colors.overdue, name: 'Просроченные' },
              { dataKey: 'Ожидающие (млн ₽)', fill: colors.pending, name: 'Ожидающие' },
            ],
          },
        };
      
      case 'donut':
        return {
          component: DonutChart,
          props: {
            data: donutData,
            centerContent: (
              <div className="text-center">
                <div className="text-lg font-bold">
                  {summary ? (summary.totalPayments / 1000000).toFixed(1) : 0}М ₽
                </div>
                <div className="text-xs text-muted-foreground">
                  Всего платежей
                </div>
              </div>
            ),
          },
        };
      
      default: // combined
        return {
          component: AreaChart,
          props: {
            areas: [
              { dataKey: 'Платежи в срок (млн ₽)', fill: colors.onTime, stroke: colors.onTime, name: 'В срок' },
              { dataKey: 'Просроченные (млн ₽)', fill: colors.overdue, stroke: colors.overdue, name: 'Просроченные' },
              { dataKey: 'Ожидающие (млн ₽)', fill: colors.pending, stroke: colors.pending, name: 'Ожидающие' },
            ],
            stacked: false,
          },
        };
    }
  };

  const chartConfig = getChartConfig();
  const ChartComponent = chartConfig.component;

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5" />
              <span>{title}</span>
            </CardTitle>
            <CardDescription>
              Анализ платежей и задолженностей
            </CardDescription>
          </div>
          
          {/* Тренды */}
          {showTrend && trends && (
            <div className="flex space-x-2">
              <Badge variant={trends.overdue <= 0 ? 'success' : 'destructive'} className="flex items-center space-x-1">
                <AlertTriangle className="h-3 w-3" />
                <span>Просрочка {trends.overdue > 0 ? '+' : ''}{trends.overdue.toFixed(1)}%</span>
              </Badge>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Сводная статистика */}
        {showSummary && summary && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {(summary.totalPayments / 1000000).toFixed(1)}М ₽
              </div>
              <div className="text-sm text-muted-foreground">Общие платежи</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 flex items-center justify-center space-x-1">
                <CheckCircle className="h-5 w-5" />
                <span>{summary.onTimeRate.toFixed(1)}%</span>
              </div>
              <div className="text-sm text-muted-foreground">В срок</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600 flex items-center justify-center space-x-1">
                <AlertTriangle className="h-5 w-5" />
                <span>{summary.overdueRate.toFixed(1)}%</span>
              </div>
              <div className="text-sm text-muted-foreground">Просрочено</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {(summary.totalPenalties / 1000).toFixed(0)}К ₽
              </div>
              <div className="text-sm text-muted-foreground">Штрафы</div>
            </div>
          </div>
        )}
        
        {/* График */}
        <div className={chartType === 'donut' ? 'h-80' : ''}>
          <ChartComponent
            data={chartType === 'donut' ? donutData : chartData}
            enableExport={true}
            showGrid={chartType !== 'donut'}
            showLegend={true}
            showTooltip={true}
            size={chartType === 'donut' ? 'md' : 'lg'}
            {...chartConfig.props}
          />
        </div>
      </CardContent>
    </Card>
  );
}
