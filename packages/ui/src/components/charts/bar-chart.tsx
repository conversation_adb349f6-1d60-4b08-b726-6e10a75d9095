/**
 * @file: bar-chart.tsx
 * @description: Базовый компонент столбчатого графика на основе Recharts
 * @dependencies: Recharts, CVA, TypeScript
 * @created: 2025-01-27
 */

"use client";

import React, { useRef } from 'react';
import {
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Download } from 'lucide-react';
import html2canvas from 'html2canvas';

// Варианты стилей для графика
const barChartVariants = cva(
  "w-full",
  {
    variants: {
      size: {
        sm: "h-64",
        md: "h-80",
        lg: "h-96",
        xl: "h-[32rem]",
      },
      variant: {
        default: "",
        minimal: "[&_.recharts-cartesian-grid]:opacity-30",
        rounded: "[&_.recharts-bar]:rx-1",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  }
);

// Типы данных
export interface ChartDataPoint {
  name: string;
  [key: string]: string | number;
}

export interface BarConfig {
  dataKey: string;
  fill: string;
  name?: string;
  stackId?: string;
  radius?: [number, number, number, number];
}

export interface BarChartProps extends VariantProps<typeof barChartVariants> {
  data: ChartDataPoint[];
  bars: BarConfig[];
  title?: string;
  showGrid?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  enableExport?: boolean;
  xAxisDataKey?: string;
  yAxisLabel?: string;
  xAxisLabel?: string;
  colors?: string[];
  layout?: 'horizontal' | 'vertical';
  barSize?: number;
  className?: string;
  onBarClick?: (data: any, index: number) => void;
}

// Цветовая палитра по умолчанию
const defaultColors = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1',
  '#d084d0', '#ffb347', '#87ceeb', '#dda0dd', '#98fb98'
];

export function BarChart({
  data,
  bars,
  title,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  enableExport = false,
  xAxisDataKey = 'name',
  yAxisLabel,
  xAxisLabel,
  colors = defaultColors,
  layout = 'vertical',
  barSize,
  size,
  variant,
  className,
  onBarClick,
}: BarChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  // Функция экспорта графика в изображение
  const handleExport = async () => {
    if (!chartRef.current) return;

    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: 'white',
        scale: 2,
      });
      
      const link = document.createElement('a');
      link.download = `${title || 'bar-chart'}-${new Date().toISOString().split('T')[0]}.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Ошибка экспорта графика:', error);
    }
  };

  // Кастомный tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Кастомная метка для столбцов
  const CustomLabel = ({ x, y, width, height, value }: any) => {
    return (
      <text
        x={x + width / 2}
        y={y - 5}
        fill="#666"
        textAnchor="middle"
        fontSize="12"
      >
        {typeof value === 'number' ? value.toLocaleString() : value}
      </text>
    );
  };

  return (
    <div className="space-y-4">
      {/* Заголовок и кнопка экспорта */}
      {(title || enableExport) && (
        <div className="flex items-center justify-between">
          {title && (
            <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          )}
          {enableExport && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              className="flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Экспорт</span>
            </Button>
          )}
        </div>
      )}

      {/* График */}
      <div 
        ref={chartRef}
        className={cn(barChartVariants({ size, variant }), className)}
      >
        <ResponsiveContainer width="100%" height="100%">
          <RechartsBarChart
            layout={layout}
            data={data}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
            onClick={onBarClick}
          >
            {showGrid && (
              <CartesianGrid 
                strokeDasharray="3 3" 
                className="stroke-muted opacity-30"
              />
            )}
            
            <XAxis 
              type={layout === 'vertical' ? 'category' : 'number'}
              dataKey={layout === 'vertical' ? xAxisDataKey : undefined}
              className="text-xs fill-muted-foreground"
              label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -5 } : undefined}
            />
            
            <YAxis 
              type={layout === 'vertical' ? 'number' : 'category'}
              dataKey={layout === 'horizontal' ? xAxisDataKey : undefined}
              className="text-xs fill-muted-foreground"
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined}
            />
            
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            
            {showLegend && (
              <Legend 
                wrapperStyle={{ paddingTop: '20px' }}
              />
            )}
            
            {bars.map((bar, index) => (
              <Bar
                key={bar.dataKey}
                dataKey={bar.dataKey}
                fill={bar.fill || colors[index % colors.length]}
                name={bar.name || bar.dataKey}
                stackId={bar.stackId}
                radius={bar.radius || [0, 0, 0, 0]}
                maxBarSize={barSize}
                label={<CustomLabel />}
              >
                {/* Градиентная заливка для каждого столбца */}
                {data.map((entry, cellIndex) => (
                  <Cell 
                    key={`cell-${cellIndex}`} 
                    fill={bar.fill || colors[index % colors.length]}
                    className="hover:opacity-80 transition-opacity cursor-pointer"
                  />
                ))}
              </Bar>
            ))}
          </RechartsBarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
