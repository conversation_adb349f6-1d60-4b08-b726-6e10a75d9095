/**
 * @file: radar-chart.tsx
 * @description: Компонент радарной диаграммы на основе Recharts
 * @dependencies: Recharts, CVA, TypeScript
 * @created: 2025-01-27
 */

"use client";

import React, { useRef } from 'react';
import {
  RadarChart as RechartsRadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Legend,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Download } from 'lucide-react';
import html2canvas from 'html2canvas';

// Варианты стилей для графика
const radarChartVariants = cva(
  "w-full",
  {
    variants: {
      size: {
        sm: "h-64",
        md: "h-80",
        lg: "h-96",
        xl: "h-[32rem]",
      },
      variant: {
        default: "",
        minimal: "[&_.recharts-polar-grid]:opacity-30",
        filled: "[&_.recharts-radar]:fill-opacity-40",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  }
);

// Типы данных
export interface RadarDataPoint {
  subject: string;
  [key: string]: string | number;
}

export interface RadarConfig {
  dataKey: string;
  stroke: string;
  fill: string;
  strokeWidth?: number;
  fillOpacity?: number;
  name?: string;
}

export interface RadarChartProps extends VariantProps<typeof radarChartVariants> {
  data: RadarDataPoint[];
  radars: RadarConfig[];
  title?: string;
  showLegend?: boolean;
  showTooltip?: boolean;
  enableExport?: boolean;
  colors?: string[];
  outerRadius?: number;
  showGrid?: boolean;
  showAngleAxis?: boolean;
  showRadiusAxis?: boolean;
  className?: string;
  onDataPointClick?: (data: any, index: number) => void;
}

// Цветовая палитра по умолчанию
const defaultColors = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1',
  '#d084d0', '#ffb347', '#87ceeb', '#dda0dd', '#98fb98'
];

export function RadarChart({
  data,
  radars,
  title,
  showLegend = true,
  showTooltip = true,
  enableExport = false,
  colors = defaultColors,
  outerRadius = 80,
  showGrid = true,
  showAngleAxis = true,
  showRadiusAxis = false,
  size,
  variant,
  className,
  onDataPointClick,
}: RadarChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  // Функция экспорта графика в изображение
  const handleExport = async () => {
    if (!chartRef.current) return;

    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: 'white',
        scale: 2,
      });
      
      const link = document.createElement('a');
      link.download = `${title || 'radar-chart'}-${new Date().toISOString().split('T')[0]}.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Ошибка экспорта графика:', error);
    }
  };

  // Кастомный tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-4">
      {/* Заголовок и кнопка экспорта */}
      {(title || enableExport) && (
        <div className="flex items-center justify-between">
          {title && (
            <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          )}
          {enableExport && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              className="flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Экспорт</span>
            </Button>
          )}
        </div>
      )}

      {/* График */}
      <div 
        ref={chartRef}
        className={cn(radarChartVariants({ size, variant }), className)}
      >
        <ResponsiveContainer width="100%" height="100%">
          <RechartsRadarChart
            cx="50%"
            cy="50%"
            outerRadius={outerRadius}
            data={data}
            onClick={onDataPointClick}
          >
            {showGrid && (
              <PolarGrid 
                className="stroke-muted opacity-30"
              />
            )}
            
            {showAngleAxis && (
              <PolarAngleAxis 
                dataKey="subject"
                className="text-xs fill-muted-foreground"
              />
            )}
            
            {showRadiusAxis && (
              <PolarRadiusAxis 
                className="text-xs fill-muted-foreground"
                angle={90}
                domain={[0, 'dataMax']}
              />
            )}
            
            {radars.map((radar, index) => (
              <Radar
                key={radar.dataKey}
                name={radar.name || radar.dataKey}
                dataKey={radar.dataKey}
                stroke={radar.stroke || colors[index % colors.length]}
                fill={radar.fill || colors[index % colors.length]}
                strokeWidth={radar.strokeWidth || 2}
                fillOpacity={radar.fillOpacity || (variant === 'filled' ? 0.4 : 0.1)}
                dot={{ r: 4, className: 'drop-shadow-sm' }}
                activeDot={{ r: 6, className: 'drop-shadow-sm' }}
              />
            ))}
            
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            
            {showLegend && (
              <Legend 
                wrapperStyle={{ paddingTop: '20px' }}
                iconType="line"
              />
            )}
          </RechartsRadarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
