/**
 * @file: area-chart.tsx
 * @description: Базовый компонент площадного графика на основе Recharts
 * @dependencies: Recharts, CVA, TypeScript
 * @created: 2025-01-27
 */

"use client";

import React, { useRef } from 'react';
import {
  AreaChart as RechartsAreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Download } from 'lucide-react';
import html2canvas from 'html2canvas';

// Варианты стилей для графика
const areaChartVariants = cva(
  "w-full",
  {
    variants: {
      size: {
        sm: "h-64",
        md: "h-80",
        lg: "h-96",
        xl: "h-[32rem]",
      },
      variant: {
        default: "",
        minimal: "[&_.recharts-cartesian-grid]:opacity-30",
        gradient: "[&_.recharts-area]:fill-opacity-60",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  }
);

// Типы данных
export interface ChartDataPoint {
  name: string;
  [key: string]: string | number;
}

export interface AreaConfig {
  dataKey: string;
  fill: string;
  stroke: string;
  strokeWidth?: number;
  name?: string;
  stackId?: string;
  type?: 'monotone' | 'linear' | 'step' | 'stepBefore' | 'stepAfter';
  fillOpacity?: number;
}

export interface AreaChartProps extends VariantProps<typeof areaChartVariants> {
  data: ChartDataPoint[];
  areas: AreaConfig[];
  title?: string;
  showGrid?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  enableExport?: boolean;
  xAxisDataKey?: string;
  yAxisLabel?: string;
  xAxisLabel?: string;
  colors?: string[];
  stacked?: boolean;
  className?: string;
  onDataPointClick?: (data: any, index: number) => void;
}

// Цветовая палитра по умолчанию
const defaultColors = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1',
  '#d084d0', '#ffb347', '#87ceeb', '#dda0dd', '#98fb98'
];

export function AreaChart({
  data,
  areas,
  title,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  enableExport = false,
  xAxisDataKey = 'name',
  yAxisLabel,
  xAxisLabel,
  colors = defaultColors,
  stacked = false,
  size,
  variant,
  className,
  onDataPointClick,
}: AreaChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  // Функция экспорта графика в изображение
  const handleExport = async () => {
    if (!chartRef.current) return;

    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: 'white',
        scale: 2,
      });
      
      const link = document.createElement('a');
      link.download = `${title || 'area-chart'}-${new Date().toISOString().split('T')[0]}.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Ошибка экспорта графика:', error);
    }
  };

  // Кастомный tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Создаем градиенты для каждой области
  const gradients = areas.map((area, index) => {
    const color = area.fill || colors[index % colors.length];
    const gradientId = `gradient-${area.dataKey}`;
    
    return (
      <defs key={gradientId}>
        <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
          <stop offset="5%" stopColor={color} stopOpacity={0.8}/>
          <stop offset="95%" stopColor={color} stopOpacity={0.1}/>
        </linearGradient>
      </defs>
    );
  });

  return (
    <div className="space-y-4">
      {/* Заголовок и кнопка экспорта */}
      {(title || enableExport) && (
        <div className="flex items-center justify-between">
          {title && (
            <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          )}
          {enableExport && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              className="flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Экспорт</span>
            </Button>
          )}
        </div>
      )}

      {/* График */}
      <div 
        ref={chartRef}
        className={cn(areaChartVariants({ size, variant }), className)}
      >
        <ResponsiveContainer width="100%" height="100%">
          <RechartsAreaChart
            data={data}
            margin={{
              top: 10,
              right: 30,
              left: 0,
              bottom: 0,
            }}
            onClick={onDataPointClick}
          >
            {gradients}
            
            {showGrid && (
              <CartesianGrid 
                strokeDasharray="3 3" 
                className="stroke-muted opacity-30"
              />
            )}
            
            <XAxis 
              dataKey={xAxisDataKey}
              className="text-xs fill-muted-foreground"
              label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -5 } : undefined}
            />
            
            <YAxis 
              className="text-xs fill-muted-foreground"
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined}
            />
            
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            
            {showLegend && (
              <Legend 
                wrapperStyle={{ paddingTop: '20px' }}
              />
            )}
            
            {areas.map((area, index) => {
              const color = area.fill || colors[index % colors.length];
              const gradientId = `gradient-${area.dataKey}`;
              
              return (
                <Area
                  key={area.dataKey}
                  type={area.type || 'monotone'}
                  dataKey={area.dataKey}
                  stackId={stacked ? 'stack' : area.stackId}
                  stroke={area.stroke || color}
                  strokeWidth={area.strokeWidth || 2}
                  fill={variant === 'gradient' ? `url(#${gradientId})` : color}
                  fillOpacity={area.fillOpacity || 0.6}
                  name={area.name || area.dataKey}
                  connectNulls={false}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5, className: 'drop-shadow-sm' }}
                />
              );
            })}
          </RechartsAreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
