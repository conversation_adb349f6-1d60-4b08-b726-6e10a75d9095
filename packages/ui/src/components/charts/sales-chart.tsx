/**
 * @file: sales-chart.tsx
 * @description: Специализированный график продаж недвижимости для PactCRM
 * @dependencies: LineChart, AreaChart, BarChart
 * @created: 2025-01-27
 */

"use client";

import React, { useMemo } from 'react';
import { LineChart } from './line-chart';
import { AreaChart } from './area-chart';
import { BarChart } from './bar-chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { TrendingUp, TrendingDown, DollarSign, Home } from 'lucide-react';

// Типы данных для продаж
export interface SalesDataPoint {
  period: string; // Месяц или квартал
  totalSales: number; // Общая сумма продаж
  unitsSold: number; // Количество проданных единиц
  averagePrice: number; // Средняя цена
  revenue: number; // Выручка
  target?: number; // Целевые показатели
}

export interface SalesChartProps {
  data: SalesDataPoint[];
  title?: string;
  chartType?: 'line' | 'area' | 'bar' | 'combined';
  showTarget?: boolean;
  showTrend?: boolean;
  showSummary?: boolean;
  period?: 'monthly' | 'quarterly' | 'yearly';
  className?: string;
}

export function SalesChart({
  data,
  title = "Продажи недвижимости",
  chartType = 'combined',
  showTarget = true,
  showTrend = true,
  showSummary = true,
  period = 'monthly',
  className,
}: SalesChartProps) {
  
  // Подготавливаем данные для графика
  const chartData = useMemo(() => {
    return data.map(item => ({
      name: item.period,
      'Продажи (млн ₽)': Math.round(item.totalSales / 1000000),
      'Количество единиц': item.unitsSold,
      'Средняя цена (млн ₽)': Math.round(item.averagePrice / 1000000),
      'Выручка (млн ₽)': Math.round(item.revenue / 1000000),
      ...(showTarget && item.target && { 'Цель (млн ₽)': Math.round(item.target / 1000000) }),
    }));
  }, [data, showTarget]);

  // Вычисляем тренды
  const trends = useMemo(() => {
    if (data.length < 2) return null;
    
    const current = data[data.length - 1];
    const previous = data[data.length - 2];
    
    const salesTrend = ((current.totalSales - previous.totalSales) / previous.totalSales) * 100;
    const unitsTrend = ((current.unitsSold - previous.unitsSold) / previous.unitsSold) * 100;
    const revenueTrend = ((current.revenue - previous.revenue) / previous.revenue) * 100;
    
    return {
      sales: salesTrend,
      units: unitsTrend,
      revenue: revenueTrend,
    };
  }, [data]);

  // Общая статистика
  const summary = useMemo(() => {
    const totalSales = data.reduce((sum, item) => sum + item.totalSales, 0);
    const totalUnits = data.reduce((sum, item) => sum + item.unitsSold, 0);
    const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0);
    const avgPrice = totalUnits > 0 ? totalSales / totalUnits : 0;
    
    return {
      totalSales,
      totalUnits,
      totalRevenue,
      avgPrice,
    };
  }, [data]);

  // Конфигурация для разных типов графиков
  const getChartConfig = () => {
    const baseColors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7c7c'];
    
    switch (chartType) {
      case 'line':
        return {
          component: LineChart,
          props: {
            lines: [
              { dataKey: 'Продажи (млн ₽)', stroke: baseColors[0], name: 'Продажи' },
              { dataKey: 'Выручка (млн ₽)', stroke: baseColors[1], name: 'Выручка' },
              ...(showTarget ? [{ dataKey: 'Цель (млн ₽)', stroke: baseColors[3], strokeDasharray: '5 5', name: 'Цель' }] : []),
            ],
          },
        };
      
      case 'area':
        return {
          component: AreaChart,
          props: {
            areas: [
              { dataKey: 'Продажи (млн ₽)', fill: baseColors[0], stroke: baseColors[0], name: 'Продажи' },
              { dataKey: 'Выручка (млн ₽)', fill: baseColors[1], stroke: baseColors[1], name: 'Выручка' },
            ],
            stacked: false,
          },
        };
      
      case 'bar':
        return {
          component: BarChart,
          props: {
            bars: [
              { dataKey: 'Продажи (млн ₽)', fill: baseColors[0], name: 'Продажи' },
              { dataKey: 'Количество единиц', fill: baseColors[2], name: 'Единицы' },
            ],
          },
        };
      
      default: // combined
        return {
          component: LineChart,
          props: {
            lines: [
              { dataKey: 'Продажи (млн ₽)', stroke: baseColors[0], name: 'Продажи' },
              { dataKey: 'Выручка (млн ₽)', stroke: baseColors[1], name: 'Выручка' },
              { dataKey: 'Средняя цена (млн ₽)', stroke: baseColors[2], name: 'Средняя цена' },
              ...(showTarget ? [{ dataKey: 'Цель (млн ₽)', stroke: baseColors[3], strokeDasharray: '5 5', name: 'Цель' }] : []),
            ],
          },
        };
    }
  };

  const chartConfig = getChartConfig();
  const ChartComponent = chartConfig.component;

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5" />
              <span>{title}</span>
            </CardTitle>
            <CardDescription>
              Динамика продаж за {period === 'monthly' ? 'месяцы' : period === 'quarterly' ? 'кварталы' : 'годы'}
            </CardDescription>
          </div>
          
          {/* Тренды */}
          {showTrend && trends && (
            <div className="flex space-x-2">
              <Badge variant={trends.sales >= 0 ? 'success' : 'destructive'} className="flex items-center space-x-1">
                {trends.sales >= 0 ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
                <span>{Math.abs(trends.sales).toFixed(1)}%</span>
              </Badge>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Сводная статистика */}
        {showSummary && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {(summary.totalSales / 1000000).toFixed(1)}М ₽
              </div>
              <div className="text-sm text-muted-foreground">Общие продажи</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {summary.totalUnits}
              </div>
              <div className="text-sm text-muted-foreground">Единиц продано</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {(summary.totalRevenue / 1000000).toFixed(1)}М ₽
              </div>
              <div className="text-sm text-muted-foreground">Выручка</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {(summary.avgPrice / 1000000).toFixed(1)}М ₽
              </div>
              <div className="text-sm text-muted-foreground">Средняя цена</div>
            </div>
          </div>
        )}
        
        {/* График */}
        <ChartComponent
          data={chartData}
          enableExport={true}
          showGrid={true}
          showLegend={true}
          showTooltip={true}
          size="lg"
          {...chartConfig.props}
        />
      </CardContent>
    </Card>
  );
}
