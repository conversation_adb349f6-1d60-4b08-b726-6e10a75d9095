/**
 * @file: login-03.tsx
 * @description: Современная страница авторизации в стиле shadcn/ui login-03
 * @dependencies: React Hook Form, Zod, Radix UI, Tailwind CSS
 * @created: 2025-01-27
 */

"use client";

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Loader2, Building2 } from 'lucide-react';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';

// Схема валидации
const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'Email обязателен')
    .email('Некорректный email адрес'),
  password: z
    .string()
    .min(1, 'Пароль обязателен')
    .min(6, 'Пароль должен содержать минимум 6 символов'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export interface Login03Props {
  onSubmit: (data: LoginFormData) => Promise<void>;
  loading?: boolean;
  error?: string;
  title?: string;
  subtitle?: string;
  logoUrl?: string;
  companyName?: string;
  showForgotPassword?: boolean;
  showSignUp?: boolean;
  onForgotPassword?: () => void;
  onSignUp?: () => void;
  className?: string;
}

export function Login03({
  onSubmit,
  loading = false,
  error,
  title = "Добро пожаловать",
  subtitle = "Войдите в свой аккаунт для продолжения",
  logoUrl,
  companyName = "PactCRM",
  showForgotPassword = true,
  showSignUp = false,
  onForgotPassword,
  onSignUp,
  className,
}: Login03Props) {
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const handleFormSubmit = async (data: LoginFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      // Ошибка обрабатывается в родительском компоненте
    }
  };

  return (
    <div className={cn("min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8", className)}>
      <div className="max-w-md w-full space-y-8">
        {/* Логотип и заголовок */}
        <div className="text-center">
          <div className="flex justify-center">
            {logoUrl ? (
              <img
                className="h-12 w-auto"
                src={logoUrl}
                alt={companyName}
              />
            ) : (
              <div className="flex items-center space-x-2">
                <Building2 className="h-8 w-8 text-primary" />
                <span className="text-2xl font-bold text-gray-900 dark:text-white">
                  {companyName}
                </span>
              </div>
            )}
          </div>
          <h2 className="mt-6 text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            {title}
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {subtitle}
          </p>
        </div>

        {/* Форма авторизации */}
        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Вход в систему</CardTitle>
            <CardDescription className="text-center">
              Введите ваши данные для входа
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
              {/* Ошибка */}
              {error && (
                <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
                  <div className="text-sm text-red-700 dark:text-red-400">
                    {error}
                  </div>
                </div>
              )}

              {/* Email */}
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Email адрес
                </label>
                <Input
                  id="email"
                  type="email"
                  autoComplete="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={cn(
                    errors.email && "border-red-500 focus-visible:ring-red-500"
                  )}
                />
                {errors.email && (
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {errors.email.message}
                  </p>
                )}
              </div>

              {/* Пароль */}
              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Пароль
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    autoComplete="current-password"
                    placeholder="Введите пароль"
                    {...register('password')}
                    className={cn(
                      "pr-10",
                      errors.password && "border-red-500 focus-visible:ring-red-500"
                    )}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* Забыли пароль */}
              {showForgotPassword && (
                <div className="flex items-center justify-end">
                  <button
                    type="button"
                    onClick={onForgotPassword}
                    className="text-sm text-primary hover:text-primary/80 font-medium"
                  >
                    Забыли пароль?
                  </button>
                </div>
              )}

              {/* Кнопка входа */}
              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting || loading}
              >
                {(isSubmitting || loading) && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Войти
              </Button>

              {/* Регистрация */}
              {showSignUp && (
                <div className="text-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Нет аккаунта?{' '}
                    <button
                      type="button"
                      onClick={onSignUp}
                      className="text-primary hover:text-primary/80 font-medium"
                    >
                      Зарегистрироваться
                    </button>
                  </span>
                </div>
              )}
            </form>
          </CardContent>
        </Card>

        {/* Дополнительная информация */}
        <div className="text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Продолжая, вы соглашаетесь с нашими{' '}
            <a href="#" className="text-primary hover:text-primary/80">
              Условиями использования
            </a>{' '}
            и{' '}
            <a href="#" className="text-primary hover:text-primary/80">
              Политикой конфиденциальности
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
