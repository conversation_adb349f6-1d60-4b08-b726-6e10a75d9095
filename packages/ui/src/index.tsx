"use client";

// Utility functions
export * from './lib/utils';

// UI Components
export * from './components/ui/button';
export * from './components/ui/card';
export * from './components/ui/avatar';
export * from './components/ui/dropdown-menu';
export * from './components/ui/navigation-menu';
export * from './components/ui/badge';
export * from './components/ui/dialog';
export * from './components/ui/input';
export * from './components/ui/select';
export * from './components/ui/switch';
export * from './components/ui/tabs';
export * from './components/ui/theme-provider';

// Layout Components
export * from './components/layout/sidebar';
export * from './components/layout/header';
export * from './components/layout/search';
export * from './components/layout/theme-switcher';
export * from './components/layout/main-layout';

// Dashboard Components
export * from './components/dashboard';

// Auth Components
export * from './components/auth';

// Global styles are imported directly in the application
