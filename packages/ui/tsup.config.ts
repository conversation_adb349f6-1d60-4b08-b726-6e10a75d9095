import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/index.tsx'],
  format: ['esm', 'cjs'],
  dts: false, // Временно отключаем DTS сборку
  external: ['react', 'react-dom', 'next'],
  sourcemap: true,
  clean: true,
  treeshake: true,
  banner: {
    js: '"use client";\n',
  },
  esbuildOptions(options) {
    // Настройки для предотвращения динамических импортов
    options.platform = 'browser';
    options.target = 'es2020';
    options.conditions = ['import', 'module'];
    options.define = {
      'process.env.NODE_ENV': '"production"'
    };
  },
});
