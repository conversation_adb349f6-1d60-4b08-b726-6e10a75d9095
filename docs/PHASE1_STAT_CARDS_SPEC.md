# 📊 Техническая спецификация: Фаза 1 - Расширение статистических компонентов

## 🎯 Обзор

**Цель**: Расширить существующий StatCard компонент и создать новые статистические компоненты для улучшения дашбордов PactCRM.

**Приоритет**: ВЫСОКИЙ  
**Сложность**: НИЗКАЯ  
**Время выполнения**: 1-2 недели  
**Ответственный**: AI Assistant + Developer

## 📋 Текущее состояние

### Существующий StatCard
```typescript
// packages/ui/src/components/dashboard/stat-card.tsx
interface StatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}
```

**Ограничения текущего компонента:**
- Только вертикальная компоновка
- Базовый дизайн без вариаций
- Ограниченные возможности кастомизации
- Нет поддержки дополнительных элементов (аватары, графики)

## 🎨 Дизайн-вдохновение из Vuexy

### Анализ компонентов Vuexy
1. **HorizontalWithBorder** - карточка с цветной границей и hover эффектами
2. **HorizontalWithAvatar** - горизонтальная компоновка с аватаром
3. **HorizontalWithSubtitle** - дополнительная информация под основным значением
4. **StatsWithAreaChart** - мини-график внутри карточки
5. **CustomerStats** - специализированная карточка для клиентов

### Ключевые дизайн-принципы Vuexy
- Цветные границы для категоризации
- Hover эффекты с анимациями
- Использование аватаров для персонализации
- Интеграция мини-графиков
- Градиенты и современные тени

## 🔧 Техническая реализация

### 1. Улучшение базового StatCard

#### 1.1 Расширение интерфейса
```typescript
interface StatCardProps {
  // Существующие свойства
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
  
  // Новые свойства
  variant?: 'default' | 'horizontal' | 'bordered' | 'gradient';
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  avatar?: {
    src?: string;
    fallback: string;
    size?: 'sm' | 'md' | 'lg';
  };
  subtitle?: string;
  chart?: {
    data: number[];
    type: 'line' | 'area' | 'bar';
    color?: string;
  };
  actions?: React.ReactNode;
  loading?: boolean;
  animated?: boolean;
}
```

#### 1.2 Варианты компонента
```typescript
// Использование class-variance-authority для вариантов
const statCardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200",
  {
    variants: {
      variant: {
        default: "p-6",
        horizontal: "p-4 flex items-center space-x-4",
        bordered: "border-l-4 p-6 hover:shadow-md hover:border-l-[6px]",
        gradient: "bg-gradient-to-br p-6 text-white border-0"
      },
      size: {
        sm: "min-h-[100px]",
        md: "min-h-[120px]", 
        lg: "min-h-[140px]"
      },
      color: {
        primary: "",
        secondary: "",
        success: "border-l-green-500 hover:border-l-green-600",
        warning: "border-l-yellow-500 hover:border-l-yellow-600",
        error: "border-l-red-500 hover:border-l-red-600",
        info: "border-l-blue-500 hover:border-l-blue-600"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      color: "primary"
    }
  }
);
```

### 2. Новые статистические компоненты

#### 2.1 HorizontalStatCard
```typescript
interface HorizontalStatCardProps extends Omit<StatCardProps, 'variant'> {
  iconPosition?: 'left' | 'right';
  iconBackground?: boolean;
}

export function HorizontalStatCard({
  title,
  value,
  icon,
  description,
  trend,
  iconPosition = 'left',
  iconBackground = true,
  color = 'primary',
  ...props
}: HorizontalStatCardProps) {
  // Горизонтальная компоновка с иконкой слева/справа
}
```

#### 2.2 StatCardWithBorder
```typescript
interface StatCardWithBorderProps extends StatCardProps {
  borderPosition?: 'left' | 'top' | 'right' | 'bottom';
  borderWidth?: 'thin' | 'medium' | 'thick';
  hoverEffect?: boolean;
}

export function StatCardWithBorder({
  borderPosition = 'left',
  borderWidth = 'medium',
  hoverEffect = true,
  ...props
}: StatCardWithBorderProps) {
  // Карточка с цветной границей и hover эффектами
}
```

#### 2.3 StatCardWithAvatar
```typescript
interface StatCardWithAvatarProps extends StatCardProps {
  avatar: {
    src?: string;
    fallback: string;
    size?: 'sm' | 'md' | 'lg';
    status?: 'online' | 'offline' | 'away' | 'busy';
  };
  avatarPosition?: 'left' | 'right' | 'top';
}

export function StatCardWithAvatar({
  avatar,
  avatarPosition = 'left',
  ...props
}: StatCardWithAvatarProps) {
  // Карточка с аватаром пользователя
}
```

#### 2.4 StatCardWithChart
```typescript
interface StatCardWithChartProps extends StatCardProps {
  chart: {
    data: number[];
    type: 'line' | 'area' | 'bar' | 'sparkline';
    color?: string;
    height?: number;
    showTooltip?: boolean;
  };
  chartPosition?: 'bottom' | 'right' | 'background';
}

export function StatCardWithChart({
  chart,
  chartPosition = 'bottom',
  ...props
}: StatCardWithChartProps) {
  // Карточка с мини-графиком
}
```

#### 2.5 KPICard
```typescript
interface KPICardProps {
  title: string;
  current: number;
  target: number;
  unit?: string;
  period?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period?: string;
  };
  progress?: {
    value: number;
    color?: string;
    showLabel?: boolean;
  };
  status?: 'excellent' | 'good' | 'warning' | 'critical';
  icon?: React.ReactNode;
  className?: string;
}

export function KPICard({
  title,
  current,
  target,
  unit = '',
  period = 'месяц',
  trend,
  progress,
  status = 'good',
  icon,
  className
}: KPICardProps) {
  // Карточка KPI с прогрессом к цели
}
```

#### 2.6 ProgressCard
```typescript
interface ProgressCardProps {
  title: string;
  value: number;
  maxValue: number;
  unit?: string;
  description?: string;
  progress: {
    percentage: number;
    color?: string;
    gradient?: boolean;
    animated?: boolean;
  };
  segments?: Array<{
    label: string;
    value: number;
    color: string;
  }>;
  icon?: React.ReactNode;
  className?: string;
}

export function ProgressCard({
  title,
  value,
  maxValue,
  unit = '',
  description,
  progress,
  segments,
  icon,
  className
}: ProgressCardProps) {
  // Карточка с прогресс-баром
}
```

#### 2.7 ComparisonCard
```typescript
interface ComparisonCardProps {
  title: string;
  current: {
    label: string;
    value: number;
    period?: string;
  };
  previous: {
    label: string;
    value: number;
    period?: string;
  };
  unit?: string;
  comparison: {
    type: 'percentage' | 'absolute';
    isPositive: boolean;
    value: number;
  };
  chart?: {
    data: number[];
    type: 'line' | 'bar';
    color?: string;
  };
  icon?: React.ReactNode;
  className?: string;
}

export function ComparisonCard({
  title,
  current,
  previous,
  unit = '',
  comparison,
  chart,
  icon,
  className
}: ComparisonCardProps) {
  // Карточка сравнения показателей
}
```

## 🎨 Дизайн-система

### Цветовая палитра
```css
:root {
  /* Основные цвета статистик */
  --stat-primary: hsl(221, 83%, 53%);
  --stat-secondary: hsl(210, 40%, 98%);
  --stat-success: hsl(142, 76%, 36%);
  --stat-warning: hsl(38, 92%, 50%);
  --stat-error: hsl(0, 84%, 60%);
  --stat-info: hsl(199, 89%, 48%);
  
  /* Градиенты */
  --stat-gradient-primary: linear-gradient(135deg, hsl(221, 83%, 53%) 0%, hsl(221, 83%, 43%) 100%);
  --stat-gradient-success: linear-gradient(135deg, hsl(142, 76%, 36%) 0%, hsl(142, 76%, 26%) 100%);
  --stat-gradient-warning: linear-gradient(135deg, hsl(38, 92%, 50%) 0%, hsl(38, 92%, 40%) 100%);
  
  /* Тени */
  --stat-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --stat-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --stat-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}
```

### Анимации
```css
@keyframes stat-card-hover {
  from {
    transform: translateY(0);
    box-shadow: var(--stat-shadow-sm);
  }
  to {
    transform: translateY(-2px);
    box-shadow: var(--stat-shadow-lg);
  }
}

@keyframes stat-value-count {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes stat-progress-fill {
  from { width: 0%; }
  to { width: var(--progress-width); }
}
```

## 📁 Структура файлов

```
packages/ui/src/components/dashboard/
├── stat-card.tsx                 # Базовый компонент (обновленный)
├── horizontal-stat-card.tsx      # Горизонтальная компоновка
├── stat-card-with-border.tsx     # С цветной границей
├── stat-card-with-avatar.tsx     # С аватаром
├── stat-card-with-chart.tsx      # С мини-графиком
├── kpi-card.tsx                  # KPI карточка
├── progress-card.tsx             # Карточка с прогрессом
├── comparison-card.tsx           # Карточка сравнения
└── index.ts                      # Экспорты
```

## 🧪 Тестирование

### Unit тесты
```typescript
// packages/ui/src/components/dashboard/__tests__/stat-card.test.tsx
describe('StatCard', () => {
  it('renders basic stat card', () => {});
  it('renders with trend indicator', () => {});
  it('renders different variants', () => {});
  it('handles loading state', () => {});
  it('supports custom colors', () => {});
});

// Аналогичные тесты для всех новых компонентов
```

### Storybook stories
```typescript
// packages/ui/src/components/dashboard/stat-card.stories.tsx
export default {
  title: 'Dashboard/StatCard',
  component: StatCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof StatCard>;

export const Default: Story = {
  args: {
    title: 'Общая выручка',
    value: '₽2,450,000',
    trend: { value: 12.5, isPositive: true },
    description: 'За текущий месяц'
  }
};

export const Horizontal: Story = {
  args: {
    ...Default.args,
    variant: 'horizontal'
  }
};

// И так далее для всех вариантов
```

## 📊 Интеграция с PactCRM

### Использование в дашбордах
```typescript
// apps/tenant-dashboard/src/app/dashboard/page.tsx
import {
  StatCard,
  HorizontalStatCard,
  StatCardWithBorder,
  KPICard,
  ProgressCard,
  ComparisonCard
} from '@pactcrm/ui';

export default function DashboardPage() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCardWithBorder
        title="Активные договоры"
        value={145}
        color="success"
        trend={{ value: 8.2, isPositive: true }}
        icon={<FileText className="h-4 w-4" />}
      />
      
      <KPICard
        title="Выполнение плана продаж"
        current={2450000}
        target={3000000}
        unit="₽"
        progress={{ percentage: 81.7, color: 'success' }}
        status="good"
      />
      
      <ProgressCard
        title="Заполненность объектов"
        value={234}
        maxValue={300}
        unit="квартир"
        progress={{ percentage: 78, animated: true }}
      />
      
      <ComparisonCard
        title="Платежи"
        current={{ label: 'Этот месяц', value: 1250000 }}
        previous={{ label: 'Прошлый месяц', value: 980000 }}
        unit="₽"
        comparison={{ type: 'percentage', isPositive: true, value: 27.6 }}
      />
    </div>
  );
}
```

## ✅ Критерии готовности

1. **Функциональность**
   - [ ] Все 7 новых компонентов реализованы
   - [ ] Базовый StatCard обновлен с новыми возможностями
   - [ ] Поддержка всех вариантов дизайна
   - [ ] Responsive дизайн на всех устройствах

2. **Качество кода**
   - [ ] TypeScript типизация 100%
   - [ ] Unit тесты покрытие >90%
   - [ ] Storybook stories для всех компонентов
   - [ ] ESLint/Prettier без ошибок

3. **Дизайн**
   - [ ] Соответствие shadcn-ui дизайн-системе
   - [ ] Поддержка темной темы
   - [ ] Анимации и переходы
   - [ ] Accessibility (a11y) соответствие

4. **Документация**
   - [ ] JSDoc комментарии для всех компонентов
   - [ ] README с примерами использования
   - [ ] Обновленная документация в Storybook

5. **Интеграция**
   - [ ] Экспорты в @pactcrm/ui пакете
   - [ ] Использование в tenant-dashboard
   - [ ] Использование в super-admin
   - [ ] Тестирование в production сборке

---

**Следующий шаг**: Начать реализацию с обновления базового StatCard компонента и создания HorizontalStatCard.
